# Float Tracker System - Installation Guide

This guide provides step-by-step instructions to install and configure the Float Tracker System from the provided VBA files.

## 📋 Prerequisites

### Required Software
- Microsoft Excel 365 (with VBA support)
- Windows 10/11 or compatible OS
- OneDrive account (recommended for AutoSave)

### Required Permissions
- Ability to enable macros in Excel
- Permission to save .xlsm files
- Administrative access to create user accounts

### Required Files
Ensure you have all these files in the same folder:
- `src/modules/FloatTracker_MainModule.bas`
- `src/modules/FloatTracker_PINModule.bas`
- `src/modules/FloatTracker_LoggingModule.bas`
- `src/modules/FloatTracker_AdminModule.bas`
- `src/modules/FloatTracker_WorksheetModule.bas`
- `src/classes/ThisWorkbook.cls`
- `src/forms/FloatForm.frm`
- `src/forms/FloatForm.frx`
- `CreateFloatTracker.vbs`

## 🚀 Installation Method 1: Automated (If Allowed on Your Computer)

> **⚠️ Note for Work Computers**: Many corporate environments restrict script execution. If you're on a work computer, skip to **Method 2: Manual Installation** below.

### Step 1: Prepare Files
1. Download all files to a single folder (e.g., `C:\FloatTracker`)
2. Ensure all source files are present in the organized structure
3. Right-click on the folder and select "Properties"
4. Uncheck "Read-only" if checked, click OK

### Step 2: Run Automated Installer
1. Right-click on `CreateFloatTracker.vbs`
2. Select "Open with" → "Microsoft Windows Based Script Host"

   **OR**

   Open Command Prompt in the folder and type:
   ```cmd
   cscript CreateFloatTracker.vbs
   ```

3. Wait for the script to complete (may take 1-2 minutes)
4. You should see messages like:
   - "Creating Float Tracker System..."
   - "Created worksheets..."
   - "Imported all VBA modules..."
   - "Setup worksheets..."
   - "Added buttons..."
   - "Saved as FloatTracker.xlsm"
   - "Float Tracker System created successfully!"

### Step 3: Verify Installation
1. Look for `FloatTracker.xlsm` in your folder
2. File size should be approximately 50-100 KB
3. If successful, proceed to "Initial Configuration" section

### Troubleshooting Automated Installation
- **If script execution is blocked**: Use Manual Installation Method 2
- **If Excel doesn't open**: Check Excel installation
- **If import errors occur**: Verify all source files are present in `/src/` folders

## 🔧 Installation Method 2: Manual (Recommended for Work Computers)

> **✅ Best for Work Computers**: This method works on corporate computers where script execution may be restricted.

### Step 1: Create New Excel Workbook
1. Open Microsoft Excel 365
2. Create a new blank workbook
3. Save as `FloatTracker.xlsm` (Excel Macro-Enabled Workbook)
4. Choose your desired location (OneDrive recommended)

### Step 2: Enable Developer Tab
1. Click File → Options
2. Select "Customize Ribbon"
3. Check "Developer" in the right panel
4. Click OK

### Step 3: Enable VBA Project Access (Critical!)
1. Still in Excel Options, click "Trust Center" → "Trust Center Settings"
2. Click "Macro Settings"
3. Check "Trust access to the VBA project object model"
4. Click OK twice to close all dialogs

   > **⚠️ Important**: Without this setting, you cannot import VBA files!

### Step 4: Create Worksheets
1. Rename "Sheet1" to "Float Queue"
2. Right-click sheet tab → Insert → Worksheet, name it "Float Log"
3. Right-click sheet tab → Insert → Worksheet, name it "UserData"
4. Right-click sheet tab → Insert → Worksheet, name it "Settings"

### Step 5: Import VBA Modules
1. Press `Alt + F11` to open VBA Editor
2. In Project Explorer (left panel), right-click on "VBAProject (FloatTracker.xlsm)"
3. Select "Import File..."
4. Navigate to your project folder and import each file **in this exact order**:

   **First, import the modules:**
   - Navigate to `src/modules/` folder
   - Select `FloatTracker_MainModule.bas` → Open
   - Repeat for each module:
     - `FloatTracker_PINModule.bas`
     - `FloatTracker_LoggingModule.bas`
     - `FloatTracker_AdminModule.bas`
     - `FloatTracker_WorksheetModule.bas`

### Step 6: Replace ThisWorkbook Class
1. In VBA Editor Project Explorer, find "ThisWorkbook" under "Microsoft Excel Objects"
2. Right-click "ThisWorkbook" → "Remove ThisWorkbook"
3. Click "No" when asked to export
4. Right-click on "VBAProject (FloatTracker.xlsm)" → "Import File..."
5. Navigate to `src/classes/` folder
6. Select `ThisWorkbook.cls` → Open

### Step 7: Import UserForm
1. Right-click on "VBAProject (FloatTracker.xlsm)" → "Import File..."
2. Navigate to `src/forms/` folder
3. Select `FloatForm.frm` → Open
4. Verify "FloatForm" appears under "Forms" in Project Explorer

### Step 8: Verify All Imports
In the VBA Editor Project Explorer, you should now see:
```
VBAProject (FloatTracker.xlsm)
├── Microsoft Excel Objects
│   ├── ThisWorkbook (ThisWorkbook)
│   ├── Sheet1 (Float Queue)
│   ├── Sheet2 (Float Log)
│   ├── Sheet3 (UserData)
│   └── Sheet4 (Settings)
├── Forms
│   └── FloatForm
└── Modules
    ├── FloatTracker_AdminModule
    ├── FloatTracker_LoggingModule
    ├── FloatTracker_MainModule
    ├── FloatTracker_PINModule
    └── FloatTracker_WorksheetModule
```

### Step 9: Save and Close VBA Editor
1. Press `Ctrl + S` to save
2. Close VBA Editor (`Alt + Q`)

### Step 10: Initialize the System
1. The system should auto-initialize when you return to Excel
2. If you see a message "Float Tracker System initialized successfully!" - you're done!
3. If no message appears, press `Alt + F8`, select "InitializeFloatTracker", click "Run"

## ⚙️ Initial Configuration

### Step 1: Enable Macros
1. Open `FloatTracker.xlsm`
2. If security warning appears, click "Enable Content"
3. If prompted about macros, select "Enable Macros"

### Step 2: Initialize System
1. The system should auto-initialize when opened
2. You should see a message: "Float Tracker System initialized successfully!"
3. If no message appears, press `Alt + F8`, select "InitializeFloatTracker", click Run

### Step 3: Verify Sheet Setup
Check that these sheets exist with proper formatting:
- **Float Queue**: Headers in blue, FLOAT and Admin Panel buttons visible
- **Float Log**: Headers in red/brown, empty data area
- **UserData**: Should be hidden (not visible in sheet tabs)
- **Settings**: Should be hidden (not visible in sheet tabs)

### Step 4: Test Sample User
1. Click the "FLOAT" button
2. Enter PIN: `1234`
3. Select any unit from dropdown
4. Click OK
5. Should see "Float processed successfully" message

## 👨‍💼 Admin Setup

### Step 1: Access Admin Panel
1. Click "Admin Panel" button on Float Queue sheet
2. Enter admin password: `FloatAdmin2024`
3. Enter your name as admin
4. Should see admin menu with options 0-9

### Step 2: Remove Sample User (Optional)
1. In admin panel, select option "3 - Remove User"
2. Enter: `Sample User`
3. Confirm removal

### Step 3: Add Real Users
For each staff member:
1. Select option "2 - Add New User"
2. Enter staff member's full name exactly as it should appear
3. **CRITICAL**: Write down the generated PIN immediately
4. Give PIN to staff member privately
5. Repeat for all staff members

### Step 4: Set Up Initial Queue Order
**Option A - Manual Order:**
1. Exit admin panel
2. Manually arrange staff names in desired order on Float Queue sheet

**Option B - Random Order:**
1. In admin panel, select option "7 - Reset Queue Order"
2. Confirm to randomize all active staff

### Step 5: Test Admin Functions
Test these functions with real users:
1. Override Float (option 1)
2. Hold Staff (option 5)
3. Release Staff (option 6)
4. View reports (options 8 & 9)

## 🔐 Security Configuration

### Step 1: Change Default Passwords (Recommended)
1. Press `Alt + F11` to open VBA Editor
2. In MainModule, find these constants and change values:
   ```vba
   ADMIN_PASSWORD = "YourNewAdminPassword"
   USERDATA_PASSWORD = "YourNewDataPassword"
   STRUCTURE_PASSWORD = "YourNewStructurePassword"
   ```
3. Save and close VBA Editor

### Step 2: Protect Workbook
1. File → Info → Protect Workbook → Protect Structure
2. Enter password (use STRUCTURE_PASSWORD value)
3. This prevents users from adding/deleting sheets

### Step 3: Backup Original File
1. Save a master copy of `FloatTracker.xlsm` in secure location
2. This serves as template for future deployments

## 🌐 Deployment

### Step 1: Choose Deployment Location

**Option A - OneDrive (Recommended):**
1. Save `FloatTracker.xlsm` to OneDrive
2. Share with appropriate staff members
3. AutoSave will be enabled automatically

**Option B - Network Drive:**
1. Save to shared network location
2. Ensure all users have read/write access
3. AutoSave will NOT be available (manual saves only)

**Option C - Local Copies:**
1. Distribute copies to individual computers
2. Requires manual synchronization of data
3. Not recommended for multi-user environment

### Step 2: User Training
1. Distribute [User Guide](docs/user-guide.md) to all staff
2. Train staff on basic FLOAT button usage
3. Emphasize PIN security and confidentiality

### Step 3: Admin Training
1. Distribute [Admin Guide](docs/admin-guide.md) to administrators
2. Train on user management functions
3. Establish backup and maintenance procedures

## ✅ Verification Checklist

### Before Going Live, Verify:
- [ ] All VBA modules imported successfully
- [ ] All 4 worksheets created with proper formatting
- [ ] FLOAT and Admin Panel buttons work
- [ ] Sample user can float successfully (PIN: 1234)
- [ ] Admin panel accessible with password
- [ ] User creation generates unique PINs
- [ ] Color coding works (orange for next in queue)
- [ ] Logging captures all actions in Float Log sheet
- [ ] Auto-save functions work after operations
- [ ] File saves properly as .xlsm format
- [ ] Macros enable automatically when file opens

### Admin Functions to Test:
- [ ] Add new user (option 2)
- [ ] Remove user (option 3)
- [ ] Reset PIN (option 4)
- [ ] Hold staff (option 5)
- [ ] Release staff (option 6)
- [ ] Reset queue (option 7)
- [ ] View reports (options 8 & 9)
- [ ] Override float (option 1)

## 🔧 Troubleshooting

### Common Issues

**"Macros are disabled"**
- **Solution**: Enable macros in Excel Trust Center settings
- File → Options → Trust Center → Trust Center Settings → Macro Settings
- Select "Enable all macros" or add folder to trusted locations

**"Compile error" when opening**
- **Solution**: One or more VBA modules didn't import correctly
- Re-import all .bas files in VBA Editor

**"FLOAT button doesn't work"**
- **Solution**: Check macro assignment
- Right-click button → Assign Macro → Select "FloatButton_Click"

**"Can't see UserData or Settings sheets"**
- **Solution**: This is normal - they are hidden for security
- To unhide: Right-click sheet tab → Unhide

**"PIN validation fails"**
- **Solution**: Check UserData sheet has encoded PINs
- Use Admin Panel to reset user PIN

**"Colors don't update"**
- **Solution**: Manually run UpdateQueueColors
- Press `Alt + F8` → UpdateQueueColors → Run

## 📞 Support Contacts

### For Technical Issues:
- Check [User Guide](docs/user-guide.md)
- Check [Admin Guide](docs/admin-guide.md)
- Contact your IT support team

### For System Administration:
- Refer to [Admin Guide](docs/admin-guide.md)
- Use emergency unlock if needed: `Alt + F11` → Immediate Window → Type: `ThisWorkbook.EmergencyUnlock`

## 🎉 Installation Complete

Your Float Tracker System is now ready for use!

### Remember to:
- Distribute user guides to staff
- Train administrators on admin functions
- Establish regular backup procedures
- Monitor system usage and logs
- Update passwords periodically

### Default Credentials for Initial Setup:
- **Sample User PIN**: 1234
- **Admin Password**: FloatAdmin2024

**⚠️ Change these immediately after installation!**
