   Begin VB.CommandButton btnCancel
      Caption         =   "Cancel"
      Height          =   495
      Left            =   2520
      TabIndex        =   5
      Top             =   3480
      Width           =   1215
   End
   Begin VB.CommandButton btnOK
      Caption         =   "OK"
      Default         =   -1  'True
      Height          =   495
      Left            =   1080
      TabIndex        =   4
      Top             =   3480
      Width           =   1215
   End
   Begin VB.ComboBox cmbUnit
      Height          =   315
      Left            =   1440
      Style           =   2  'Dropdown List
      TabIndex        =   3
      Top             =   2520
      Width           =   2535
   End
   Begin VB.TextBox txtPIN
      Height          =   315
      IMEMode         =   3  'DISABLE
      Left            =   1440
      MaxLength       =   4
      PasswordChar    =   "*"
      TabIndex        =   1
      Top             =   1680
      Width           =   1215
   End
   Begin VB.Label lblInstructions
      Caption         =   "Enter your 4-digit PIN and select the unit you are floating to."
      Height          =   495
      Left            =   480
      TabIndex        =   6
      Top             =   480
      Width           =   3735
   End
   Begin VB.Label lblUnit
      Caption         =   "Float to Unit:"
      Height          =   255
      Left            =   480
      TabIndex        =   2
      Top             =   2520
      Width           =   1095
   End
   Begin VB.Label lblPIN
      Caption         =   "Your PIN:"
      Height          =   255
      Left            =   480
      TabIndex        =   0
      Top             =   1680
      Width           =   855
   End