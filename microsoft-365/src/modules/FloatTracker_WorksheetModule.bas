Attribute VB_Name = "WorksheetModule"
' ===================================================================
' FLOAT TRACKER SYSTEM - WORKSHEET SETUP MODULE
' Initialize and configure all worksheets
' ===================================================================

Option Explicit

' ===================================================================
' WORKBOOK INITIALIZATION
' ===================================================================
Sub InitializeFloatTracker()
    On Error GoTo ErrorHandler
    
    Application.ScreenUpdating = False
    
    ' Initialize all sheets
    Call SetupFloatQueueSheet()
    Call SetupFloatLogSheet()
    Call SetupUserDataSheet()
    Call SetupSettingsSheet()
    
    ' Protect workbook structure
    Call ProtectWorkbook()
    
    ' Show welcome message
    MsgBox "Float Tracker System initialized successfully!" & vbCrLf & vbCrLf & _
           "• Click the FLOAT button to submit a float request" & vbCrLf & _
           "• Admins can access admin functions via the Admin Panel button" & vbCrLf & vbCrLf & _
           "For help, contact your system administrator.", _
           vbInformation, "Float Tracker Ready"
    
    Application.ScreenUpdating = True
    
    Exit Sub
    
ErrorHandler:
    Application.ScreenUpdating = True
    MsgBox "Error initializing Float Tracker: " & Err.Description, vbCritical
End Sub

' ===================================================================
' FLOAT QUEUE SHEET SETUP
' ===================================================================
Sub SetupFloatQueueSheet()
    On Error GoTo ErrorHandler
    
    Dim ws As Worksheet
    Dim floatButton As Button
    Dim adminButton As Button
    
    ' Get or create sheet
    On Error Resume Next
    Set ws = ThisWorkbook.Sheets("Float Queue")
    On Error GoTo ErrorHandler
    
    If ws Is Nothing Then
        Set ws = ThisWorkbook.Sheets.Add
        ws.Name = "Float Queue"
    End If
    
    ' Clear existing content
    ws.Cells.Clear
    
    ' Set up headers
    With ws
        .Cells(1, 1).Value = "Staff Name"
        .Cells(1, 2).Value = "Last Floated"
        .Cells(1, 3).Value = "Float Unit"
        .Cells(1, 4).Value = "Status"
        .Cells(1, 5).Value = "Hold Reason"
        
        ' Format headers
        .Range("A1:E1").Font.Bold = True
        .Range("A1:E1").Font.Size = 12
        .Range("A1:E1").Interior.Color = RGB(100, 150, 200)
        .Range("A1:E1").Font.Color = RGB(255, 255, 255)
        .Range("A1:E1").Borders.LineStyle = xlContinuous
        .Range("A1:E1").HorizontalAlignment = xlCenter
        
        ' Set column widths
        .Columns("A").ColumnWidth = 20  ' Staff Name
        .Columns("B").ColumnWidth = 18  ' Last Floated
        .Columns("C").ColumnWidth = 15  ' Float Unit
        .Columns("D").ColumnWidth = 12  ' Status
        .Columns("E").ColumnWidth = 25  ' Hold Reason
        
        ' Add sample data if sheet is empty
        If .Cells(2, 1).Value = "" Then
            .Cells(2, 1).Value = "Sample User"
            .Cells(2, 2).Value = ""
            .Cells(2, 3).Value = ""
            .Cells(2, 4).Value = "Active"
            .Cells(2, 5).Value = ""
        End If
    End With
    
    ' Add FLOAT button
    On Error Resume Next
    Set floatButton = ws.Buttons("FloatButton")
    If floatButton Is Nothing Then
        Set floatButton = ws.Buttons.Add(50, 50, 100, 30)
        floatButton.Name = "FloatButton"
    End If
    On Error GoTo ErrorHandler
    
    With floatButton
        .Text = "FLOAT"
        .OnAction = "FloatButton_Click"
        .Font.Bold = True
        .Font.Size = 12
    End With
    
    ' Add Admin Panel button
    On Error Resume Next
    Set adminButton = ws.Buttons("AdminButton")
    If adminButton Is Nothing Then
        Set adminButton = ws.Buttons.Add(200, 50, 120, 30)
        adminButton.Name = "AdminButton"
    End If
    On Error GoTo ErrorHandler
    
    With adminButton
        .Text = "Admin Panel"
        .OnAction = "ShowAdminPanel"
        .Font.Bold = True
        .Font.Size = 10
    End With
    
    ' Protect sheet but allow button clicks
    ws.Protect Password:=USERDATA_PASSWORD, UserInterfaceOnly:=True
    
    Exit Sub
    
ErrorHandler:
    MsgBox "Error setting up Float Queue sheet: " & Err.Description, vbCritical
End Sub

' ===================================================================
' FLOAT LOG SHEET SETUP
' ===================================================================
Sub SetupFloatLogSheet()
    On Error GoTo ErrorHandler
    
    Dim ws As Worksheet
    
    ' Get or create sheet
    On Error Resume Next
    Set ws = ThisWorkbook.Sheets("Float Log")
    On Error GoTo ErrorHandler
    
    If ws Is Nothing Then
        Set ws = ThisWorkbook.Sheets.Add
        ws.Name = "Float Log"
    End If
    
    ' Set up headers if they don't exist
    If ws.Cells(1, 1).Value = "" Then
        With ws
            .Cells(1, 1).Value = "Log ID"
            .Cells(1, 2).Value = "Date/Time"
            .Cells(1, 3).Value = "Staff Name"
            .Cells(1, 4).Value = "Unit"
            .Cells(1, 5).Value = "Queue Position"
            .Cells(1, 6).Value = "Action Type"
            .Cells(1, 7).Value = "Admin Name"
            .Cells(1, 8).Value = "Notes/Reason"
            
            ' Format headers
            .Range("A1:H1").Font.Bold = True
            .Range("A1:H1").Font.Size = 11
            .Range("A1:H1").Interior.Color = RGB(150, 100, 100)
            .Range("A1:H1").Font.Color = RGB(255, 255, 255)
            .Range("A1:H1").Borders.LineStyle = xlContinuous
            .Range("A1:H1").HorizontalAlignment = xlCenter
            
            ' Set column widths
            .Columns("A").ColumnWidth = 8   ' Log ID
            .Columns("B").ColumnWidth = 18  ' Date/Time
            .Columns("C").ColumnWidth = 15  ' Staff Name
            .Columns("D").ColumnWidth = 12  ' Unit
            .Columns("E").ColumnWidth = 12  ' Queue Position
            .Columns("F").ColumnWidth = 18  ' Action Type
            .Columns("G").ColumnWidth = 12  ' Admin Name
            .Columns("H").ColumnWidth = 30  ' Notes/Reason
        End With
    End If
    
    ' Protect sheet from editing
    Call ProtectLogSheet()
    
    Exit Sub
    
ErrorHandler:
    MsgBox "Error setting up Float Log sheet: " & Err.Description, vbCritical
End Sub

' ===================================================================
' USER DATA SHEET SETUP
' ===================================================================
Sub SetupUserDataSheet()
    On Error GoTo ErrorHandler
    
    Dim ws As Worksheet
    
    ' Get or create sheet
    On Error Resume Next
    Set ws = ThisWorkbook.Sheets("UserData")
    On Error GoTo ErrorHandler
    
    If ws Is Nothing Then
        Set ws = ThisWorkbook.Sheets.Add
        ws.Name = "UserData"
    End If
    
    ' Set up headers if they don't exist
    If ws.Cells(1, 1).Value = "" Then
        With ws
            .Cells(1, 1).Value = "Staff Name"
            .Cells(1, 2).Value = "Encoded PIN"
            .Cells(1, 3).Value = "Date Created"
            .Cells(1, 4).Value = "Created By"
            .Cells(1, 5).Value = "Status"
            
            ' Format headers
            .Range("A1:E1").Font.Bold = True
            .Range("A1:E1").Interior.Color = RGB(200, 200, 200)
            .Range("A1:E1").Borders.LineStyle = xlContinuous
            
            ' Set column widths
            .Columns("A").ColumnWidth = 20  ' Staff Name
            .Columns("B").ColumnWidth = 15  ' Encoded PIN
            .Columns("C").ColumnWidth = 18  ' Date Created
            .Columns("D").ColumnWidth = 15  ' Created By
            .Columns("E").ColumnWidth = 12  ' Status
            
            ' Add sample user if none exists
            .Cells(2, 1).Value = "Sample User"
            .Cells(2, 2).Value = EncodePIN("1234")  ' Sample PIN: 1234
            .Cells(2, 3).Value = Now()
            .Cells(2, 4).Value = "System"
            .Cells(2, 5).Value = "Active"
        End With
    End If
    
    ' Hide sheet completely
    ws.Visible = xlSheetVeryHidden
    
    ' Protect sheet
    ws.Protect Password:=USERDATA_PASSWORD, UserInterfaceOnly:=True
    
    Exit Sub
    
ErrorHandler:
    MsgBox "Error setting up UserData sheet: " & Err.Description, vbCritical
End Sub

' ===================================================================
' SETTINGS SHEET SETUP
' ===================================================================
Sub SetupSettingsSheet()
    On Error GoTo ErrorHandler
    
    Dim ws As Worksheet
    
    ' Get or create sheet
    On Error Resume Next
    Set ws = ThisWorkbook.Sheets("Settings")
    On Error GoTo ErrorHandler
    
    If ws Is Nothing Then
        Set ws = ThisWorkbook.Sheets.Add
        ws.Name = "Settings"
    End If
    
    ' Set up configuration if it doesn't exist
    If ws.Cells(1, 1).Value = "" Then
        With ws
            .Cells(1, 1).Value = "Float Tracker Configuration"
            .Cells(1, 1).Font.Bold = True
            .Cells(1, 1).Font.Size = 14
            
            .Cells(3, 1).Value = "Available Units:"
            .Cells(3, 1).Font.Bold = True
            
            ' Default units for this hospital
            .Cells(4, 1).Value = "EW1"
            .Cells(5, 1).Value = "EW2"
            .Cells(6, 1).Value = "ICU"
            .Cells(7, 1).Value = "SR"
            .Cells(8, 1).Value = "NR"
            .Cells(9, 1).Value = "SBU"
            .Cells(10, 1).Value = "CAP"
            
            .Cells(15, 1).Value = "System Information:"
            .Cells(15, 1).Font.Bold = True
            .Cells(16, 1).Value = "Version: 1.0"
            .Cells(17, 1).Value = "Created: " & Format(Now(), "mm/dd/yyyy")
            .Cells(18, 1).Value = "Last Updated: " & Format(Now(), "mm/dd/yyyy hh:mm")
        End With
    End If
    
    ' Hide settings sheet from regular users
    ws.Visible = xlSheetHidden
    
    Exit Sub
    
ErrorHandler:
    MsgBox "Error setting up Settings sheet: " & Err.Description, vbCritical
End Sub

' ===================================================================
' WORKBOOK PROTECTION
' ===================================================================
Sub ProtectWorkbook()
    On Error GoTo ErrorHandler
    
    ' Protect workbook structure
    ThisWorkbook.Protect Password:=STRUCTURE_PASSWORD, Structure:=True, Windows:=False
    
    Exit Sub
    
ErrorHandler:
    MsgBox "Error protecting workbook: " & Err.Description, vbCritical
End Sub
