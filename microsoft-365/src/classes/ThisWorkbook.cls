VERSION 1.0 CLASS
BEGIN
  MultiUse = -1  'True
END
Attribute VB_Name = "ThisWorkbook"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = True

' ===================================================================
' FLOAT TRACKER SYSTEM - WORKBOOK EVENT HANDLERS
' Handles workbook-level events and initialization
' ===================================================================

Option Explicit

' ===================================================================
' WORKBOOK EVENTS
' ===================================================================
Private Sub Workbook_Open()
    On Error GoTo ErrorHandler
    
    Application.ScreenUpdating = False
    
    ' Initialize the system
    Call InitializeFloatTracker()
    
    ' Set up event handlers
    Call SetupEventHandlers()
    
    ' Show the main sheet
    ThisWorkbook.Sheets("Float Queue").Activate
    
    Application.ScreenUpdating = True
    
    Exit Sub
    
ErrorHandler:
    Application.ScreenUpdating = True
    MsgBox "Error opening Float Tracker: " & Err.Description & vbCrLf & vbCrLf & _
           "Please contact your system administrator.", vbCritical, "Startup Error"
End Sub

Private Sub Workbook_BeforeClose(Cancel As Boolean)
    On Error GoTo ErrorHandler
    
    ' Clean up any temporary data
    CurrentAdminUser = ""
    
    ' Save the workbook
    If Not ThisWorkbook.Saved Then
        Dim response As VbMsgBoxResult
        response = MsgBox("Do you want to save changes to the Float Tracker?", _
                         vbYesNoCancel + vbQuestion, "Save Changes")
        
        Select Case response
            Case vbYes
                ThisWorkbook.Save
            Case vbCancel
                Cancel = True
                Exit Sub
        End Select
    End If
    
    Exit Sub
    
ErrorHandler:
    MsgBox "Error closing Float Tracker: " & Err.Description, vbCritical
End Sub

Private Sub Workbook_BeforeSave(ByVal SaveAsUI As Boolean, Cancel As Boolean)
    On Error GoTo ErrorHandler
    
    ' Update last modified timestamp in settings
    Dim settingsSheet As Worksheet
    Set settingsSheet = ThisWorkbook.Sheets("Settings")
    settingsSheet.Cells(18, 1).Value = "Last Updated: " & Format(Now(), "mm/dd/yyyy hh:mm")
    
    Exit Sub
    
ErrorHandler:
    MsgBox "Error during save: " & Err.Description, vbCritical
End Sub

Private Sub Workbook_SheetActivate(ByVal Sh As Object)
    On Error GoTo ErrorHandler
    
    ' Update colors when Float Queue sheet is activated
    If Sh.Name = "Float Queue" Then
        Call UpdateQueueColors()
    End If
    
    Exit Sub
    
ErrorHandler:
    ' Ignore errors in this event
End Sub

' ===================================================================
' INITIALIZATION HELPERS
' ===================================================================
Private Sub SetupEventHandlers()
    On Error GoTo ErrorHandler
    
    ' Enable events
    Application.EnableEvents = True
    
    ' Set calculation mode
    Application.Calculation = xlCalculationAutomatic
    
    ' Set up worksheet protection
    Call ProtectAllSheets()
    
    Exit Sub
    
ErrorHandler:
    MsgBox "Error setting up event handlers: " & Err.Description, vbCritical
End Sub

Private Sub ProtectAllSheets()
    On Error Resume Next
    
    Dim ws As Worksheet
    
    For Each ws In ThisWorkbook.Worksheets
        Select Case ws.Name
            Case "Float Queue"
                ' Allow button clicks but protect data
                ws.Protect Password:=USERDATA_PASSWORD, UserInterfaceOnly:=True
                
            Case "Float Log"
                ' Fully protect log sheet
                Call ProtectLogSheet()
                
            Case "UserData"
                ' Hide and protect user data
                ws.Visible = xlSheetVeryHidden
                ws.Protect Password:=USERDATA_PASSWORD, UserInterfaceOnly:=True
                
            Case "Settings"
                ' Hide settings from regular users
                ws.Visible = xlSheetHidden
                ws.Protect Password:=USERDATA_PASSWORD
        End Select
    Next ws
    
    On Error GoTo 0
End Sub

' ===================================================================
' SECURITY FUNCTIONS
' ===================================================================
Private Sub Workbook_SheetChange(ByVal Sh As Object, ByVal Target As Range)
    On Error GoTo ErrorHandler
    
    ' Prevent unauthorized changes to critical sheets
    If Sh.Name = "Float Log" Or Sh.Name = "UserData" Then
        Application.EnableEvents = False
        Application.Undo
        Application.EnableEvents = True
        MsgBox "This sheet is protected and cannot be modified manually.", vbExclamation
        Exit Sub
    End If
    
    ' Update colors if Float Queue data changed
    If Sh.Name = "Float Queue" Then
        Call UpdateQueueColors()
    End If
    
    Exit Sub
    
ErrorHandler:
    Application.EnableEvents = True
End Sub

Private Sub Workbook_SheetSelectionChange(ByVal Sh As Object, ByVal Target As Range)
    On Error GoTo ErrorHandler
    
    ' Prevent selection of sensitive areas
    If Sh.Name = "UserData" Then
        ' Don't allow selection of PIN column
        If Target.Column = 2 Then
            Sh.Cells(1, 1).Select
        End If
    End If
    
    Exit Sub
    
ErrorHandler:
    ' Ignore errors in selection change
End Sub

' ===================================================================
' UTILITY FUNCTIONS
' ===================================================================
Public Sub RefreshSystem()
    On Error GoTo ErrorHandler
    
    Application.ScreenUpdating = False
    
    ' Update queue colors
    Call UpdateQueueColors()
    
    ' Refresh calculations
    Application.Calculate
    
    Application.ScreenUpdating = True
    
    MsgBox "System refreshed successfully.", vbInformation
    
    Exit Sub
    
ErrorHandler:
    Application.ScreenUpdating = True
    MsgBox "Error refreshing system: " & Err.Description, vbCritical
End Sub

Public Sub ShowSystemInfo()
    Dim info As String
    
    info = "FLOAT TRACKER SYSTEM INFORMATION" & vbCrLf & vbCrLf & _
           "Version: 1.0" & vbCrLf & _
           "Created: " & Format(Now(), "mm/dd/yyyy") & vbCrLf & _
           "Excel Version: " & Application.Version & vbCrLf & vbCrLf & _
           "Features:" & vbCrLf & _
           "• Secure PIN-based authentication" & vbCrLf & _
           "• Automatic queue management" & vbCrLf & _
           "• Comprehensive logging" & vbCrLf & _
           "• Admin override capabilities" & vbCrLf & _
           "• Staff hold management" & vbCrLf & _
           "• Smart missed float detection" & vbCrLf & vbCrLf & _
           "For support, contact your system administrator."
    
    MsgBox info, vbInformation, "System Information"
End Sub

' ===================================================================
' EMERGENCY FUNCTIONS
' ===================================================================
Public Sub EmergencyUnlock()
    On Error Resume Next
    
    Dim password As String
    password = InputBox("Enter emergency unlock password:", "Emergency Unlock")
    
    If password = "EMERGENCY" & ADMIN_PASSWORD Then
        ' Unprotect all sheets
        Dim ws As Worksheet
        For Each ws In ThisWorkbook.Worksheets
            ws.Unprotect Password:=USERDATA_PASSWORD
            ws.Visible = xlSheetVisible
        Next ws
        
        ' Unprotect workbook
        ThisWorkbook.Unprotect Password:=STRUCTURE_PASSWORD
        
        MsgBox "Emergency unlock completed. All protections removed.", vbExclamation
    Else
        MsgBox "Invalid emergency password.", vbCritical
    End If
    
    On Error GoTo 0
End Sub

Public Sub RestoreProtection()
    On Error Resume Next
    
    ' Restore all protections
    Call ProtectAllSheets()
    Call ProtectWorkbook()
    
    MsgBox "Protection restored.", vbInformation
    
    On Error GoTo 0
End Sub
