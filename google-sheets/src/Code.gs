/**
 * Float Tracker System - Main Code Module
 * Google Sheets + Apps Script Implementation
 * 
 * This is the main entry point for the Float Tracker System.
 * Handles initialization, main UI functions, and coordinates other modules.
 */

// Global constants for system configuration
const ADMIN_PASSWORD_DEFAULT = 'FloatAdmin2024';
const SAMPLE_USER_PIN = '1234';
const VERSION = '1.0.0';

// Sheet names - these must match the actual sheet names in your Google Sheets
const SHEET_NAMES = {
  QUEUE: 'Float Queue',
  LOG: 'Float Log', 
  USERDATA: 'UserData',
  SETTINGS: 'Settings'
};

// Column indices for Float Queue sheet (0-based)
const QUEUE_COLUMNS = {
  STAFF_NAME: 0,    // Column A
  LAST_FLOATED: 1,  // Column B  
  FLOAT_UNIT: 2,    // Column C
  STATUS: 3,        // Column D
  HOLD_REASON: 4    // Column E
};

// Column indices for Float Log sheet (0-based)
const LOG_COLUMNS = {
  TIMESTAMP: 0,     // Column A
  STAFF_NAME: 1,    // Column B
  ACTION: 2,        // Column C
  UNIT: 3,          // Column D
  ADMIN: 4,         // Column E
  NOTES: 5          // Column F
};

/**
 * Initialize the Float Tracker System
 * Called when the spreadsheet is first opened or manually triggered
 */
function initializeFloatTracker() {
  try {
    console.log('Initializing Float Tracker System...');
    
    // Get the active spreadsheet
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    
    // Verify all required sheets exist
    if (!verifySheetStructure(ss)) {
      throw new Error('Required sheets are missing. Please check installation.');
    }
    
    // Set up initial data if needed
    setupInitialData(ss);
    
    // Update queue colors to show current status
    updateQueueColors();
    
    // Log system initialization
    logAction('System', 'System Initialized', '', 'System', 'Float Tracker v' + VERSION);
    
    console.log('Float Tracker System initialized successfully!');
    SpreadsheetApp.getUi().alert('Float Tracker System initialized successfully!');
    
  } catch (error) {
    console.error('Initialization failed:', error);
    SpreadsheetApp.getUi().alert('Initialization failed: ' + error.message);
  }
}

/**
 * Verify that all required sheets exist with proper structure
 */
function verifySheetStructure(ss) {
  const requiredSheets = Object.values(SHEET_NAMES);
  
  for (const sheetName of requiredSheets) {
    const sheet = ss.getSheetByName(sheetName);
    if (!sheet) {
      console.error(`Missing required sheet: ${sheetName}`);
      return false;
    }
  }
  
  console.log('All required sheets found');
  return true;
}

/**
 * Set up initial data if the system is being run for the first time
 */
function setupInitialData(ss) {
  // Set up sample user if UserData sheet is empty
  const userDataSheet = ss.getSheetByName(SHEET_NAMES.USERDATA);
  const userData = userDataSheet.getDataRange().getValues();
  
  if (userData.length <= 1) { // Only headers exist
    console.log('Setting up sample user...');
    addSampleUser();
  }
  
  // Set up settings if Settings sheet is empty
  const settingsSheet = ss.getSheetByName(SHEET_NAMES.SETTINGS);
  const settingsData = settingsSheet.getDataRange().getValues();
  
  if (settingsData.length <= 1) { // Only headers exist
    console.log('Setting up default settings...');
    setupDefaultSettings();
  }
}

/**
 * Add sample user for testing purposes
 */
function addSampleUser() {
  const userDataSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.USERDATA);
  const queueSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.QUEUE);
  
  // Add to UserData sheet (PIN hash for "1234")
  const pinHash = hashPIN(SAMPLE_USER_PIN);
  userDataSheet.appendRow([
    'Sample User',
    pinHash,
    new Date(),
    ''
  ]);
  
  // Add to Queue sheet
  queueSheet.appendRow([
    'Sample User',
    '',
    '',
    'Active',
    ''
  ]);
  
  console.log('Sample user added successfully');
}

/**
 * Set up default system settings
 */
function setupDefaultSettings() {
  const settingsSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.SETTINGS);
  
  settingsSheet.getRange('A1:B6').setValues([
    ['Setting', 'Value'],
    ['ADMIN_PASSWORD', ADMIN_PASSWORD_DEFAULT],
    ['SYSTEM_VERSION', VERSION],
    ['LAST_BACKUP', new Date()],
    ['TOTAL_FLOATS', 0],
    ['SYSTEM_CREATED', new Date()]
  ]);
  
  console.log('Default settings configured');
}

/**
 * Main function to show the Float dialog when FLOAT button is clicked
 * This function should be assigned to the FLOAT button
 */
function showFloatDialog() {
  try {
    console.log('Float dialog requested');
    
    // Create HTML dialog for PIN entry and unit selection
    const htmlOutput = HtmlService.createHtmlOutput(`
      <!DOCTYPE html>
      <html>
        <head>
          <base target="_top">
          <style>
            body { font-family: Arial, sans-serif; padding: 20px; }
            .form-group { margin-bottom: 15px; }
            label { display: block; margin-bottom: 5px; font-weight: bold; }
            input, select { width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; }
            button { background-color: #4285f4; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px; }
            button:hover { background-color: #3367d6; }
            .cancel { background-color: #ea4335; }
            .cancel:hover { background-color: #d33b2c; }
          </style>
        </head>
        <body>
          <h3>Submit Float Request</h3>
          <form id="floatForm">
            <div class="form-group">
              <label for="pin">Enter your 4-digit PIN:</label>
              <input type="password" id="pin" name="pin" maxlength="4" pattern="[0-9]{4}" required>
            </div>
            <div class="form-group">
              <label for="unit">Select destination unit:</label>
              <select id="unit" name="unit" required>
                <option value="">-- Select Unit --</option>
                <option value="ICU">ICU</option>
                <option value="ER">Emergency Room</option>
                <option value="Med/Surg">Medical/Surgical</option>
                <option value="Pediatrics">Pediatrics</option>
                <option value="Maternity">Maternity</option>
                <option value="OR">Operating Room</option>
                <option value="Recovery">Recovery</option>
                <option value="Oncology">Oncology</option>
                <option value="Cardiology">Cardiology</option>
                <option value="Other">Other</option>
              </select>
            </div>
            <button type="button" onclick="submitFloat()">Submit Float</button>
            <button type="button" class="cancel" onclick="google.script.host.close()">Cancel</button>
          </form>
          
          <script>
            function submitFloat() {
              const pin = document.getElementById('pin').value;
              const unit = document.getElementById('unit').value;
              
              if (!pin || !unit) {
                alert('Please enter your PIN and select a unit.');
                return;
              }
              
              if (pin.length !== 4 || !/^[0-9]+$/.test(pin)) {
                alert('PIN must be exactly 4 digits.');
                return;
              }
              
              // Call the server-side function to process the float
              google.script.run
                .withSuccessHandler(onSuccess)
                .withFailureHandler(onFailure)
                .processFloat(pin, unit);
            }
            
            function onSuccess(result) {
              if (result.success) {
                alert('Float processed successfully!\\n\\n' + result.message);
                google.script.host.close();
              } else {
                alert('Error: ' + result.message);
              }
            }
            
            function onFailure(error) {
              alert('System error: ' + error.message);
            }
            
            // Auto-focus on PIN field
            document.getElementById('pin').focus();
          </script>
        </body>
      </html>
    `).setWidth(400).setHeight(300);
    
    SpreadsheetApp.getUi().showModalDialog(htmlOutput, 'Float Request');
    
  } catch (error) {
    console.error('Error showing float dialog:', error);
    SpreadsheetApp.getUi().alert('Error: ' + error.message);
  }
}

/**
 * Process a float request from the dialog
 * @param {string} pin - The user's 4-digit PIN
 * @param {string} unit - The destination unit
 * @return {Object} Result object with success status and message
 */
function processFloat(pin, unit) {
  try {
    console.log(`Processing float request for unit: ${unit}`);
    
    // Validate PIN and get user
    const user = validatePIN(pin);
    if (!user) {
      return { success: false, message: 'Invalid PIN. Please check your PIN and try again.' };
    }
    
    // Check if user is on hold
    if (isUserOnHold(user.name)) {
      return { success: false, message: 'You are currently on hold and cannot float. Please contact an administrator.' };
    }
    
    // Process the float
    const result = executeFloat(user.name, unit);
    
    if (result.success) {
      // Update queue colors to reflect new status
      updateQueueColors();
      
      // Log the action
      logAction(user.name, 'Float Submitted', unit, user.name, 'Self-service float');
      
      return { 
        success: true, 
        message: `Float to ${unit} processed successfully!\n\nYou have been moved to the bottom of the queue.` 
      };
    } else {
      return result;
    }
    
  } catch (error) {
    console.error('Error processing float:', error);
    return { success: false, message: 'System error: ' + error.message };
  }
}

/**
 * Execute the float operation - update queue and timestamps
 * @param {string} staffName - Name of the staff member floating
 * @param {string} unit - Destination unit
 * @return {Object} Result object with success status
 */
function executeFloat(staffName, unit) {
  try {
    const queueSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.QUEUE);
    const data = queueSheet.getDataRange().getValues();
    
    // Find the user in the queue
    let userRowIndex = -1;
    for (let i = 1; i < data.length; i++) { // Skip header row
      if (data[i][QUEUE_COLUMNS.STAFF_NAME] === staffName) {
        userRowIndex = i;
        break;
      }
    }
    
    if (userRowIndex === -1) {
      return { success: false, message: 'User not found in queue.' };
    }
    
    // Update the user's record
    const now = new Date();
    queueSheet.getRange(userRowIndex + 1, QUEUE_COLUMNS.LAST_FLOATED + 1).setValue(now);
    queueSheet.getRange(userRowIndex + 1, QUEUE_COLUMNS.FLOAT_UNIT + 1).setValue(unit);
    
    // Move user to bottom of queue (reorder)
    reorderQueue(staffName);
    
    console.log(`Float executed successfully for ${staffName} to ${unit}`);
    return { success: true };
    
  } catch (error) {
    console.error('Error executing float:', error);
    return { success: false, message: 'Failed to execute float: ' + error.message };
  }
}

/**
 * Reorder the queue by moving the specified user to the bottom
 * @param {string} staffName - Name of staff member to move to bottom
 */
function reorderQueue(staffName) {
  try {
    const queueSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.QUEUE);
    const data = queueSheet.getDataRange().getValues();
    
    // Find and remove the user's row
    let userRow = null;
    let userRowIndex = -1;
    
    for (let i = 1; i < data.length; i++) { // Skip header row
      if (data[i][QUEUE_COLUMNS.STAFF_NAME] === staffName) {
        userRow = data[i];
        userRowIndex = i;
        break;
      }
    }
    
    if (userRow && userRowIndex > 0) {
      // Remove user from current position
      data.splice(userRowIndex, 1);
      
      // Add user to the end
      data.push(userRow);
      
      // Clear the sheet and write the reordered data
      queueSheet.clear();
      queueSheet.getRange(1, 1, data.length, data[0].length).setValues(data);
      
      // Restore formatting for headers
      const headerRange = queueSheet.getRange(1, 1, 1, data[0].length);
      headerRange.setFontWeight('bold');
      headerRange.setBackground('#4285f4');
      headerRange.setFontColor('#ffffff');
      
      console.log(`Queue reordered - ${staffName} moved to bottom`);
    }
    
  } catch (error) {
    console.error('Error reordering queue:', error);
    throw error;
  }
}

/**
 * Update queue colors to highlight who's next to float
 * Next person in queue gets orange highlighting
 */
function updateQueueColors() {
  try {
    const queueSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.QUEUE);
    const data = queueSheet.getDataRange().getValues();
    
    if (data.length <= 1) return; // No data rows
    
    // Clear existing background colors (except header)
    const dataRange = queueSheet.getRange(2, 1, data.length - 1, data[0].length);
    dataRange.setBackground('#ffffff');
    
    // Find first active user (next to float)
    for (let i = 1; i < data.length; i++) {
      const status = data[i][QUEUE_COLUMNS.STATUS];
      if (status === 'Active') {
        // Highlight this row in orange
        const rowRange = queueSheet.getRange(i + 1, 1, 1, data[0].length);
        rowRange.setBackground('#ff9900'); // Orange background
        rowRange.setFontColor('#000000'); // Black text for readability
        break; // Only highlight the first active user
      }
    }
    
    console.log('Queue colors updated');
    
  } catch (error) {
    console.error('Error updating queue colors:', error);
  }
}

/**
 * Event handler for spreadsheet edits
 * Automatically updates colors when data changes
 */
function onEdit(e) {
  try {
    const sheet = e.source.getActiveSheet();
    
    // Only process edits to the Float Queue sheet
    if (sheet.getName() === SHEET_NAMES.QUEUE) {
      // Small delay to ensure edit is complete
      Utilities.sleep(100);
      updateQueueColors();
    }
    
  } catch (error) {
    console.error('Error in onEdit handler:', error);
  }
}

/**
 * Manual function to refresh the system
 * Can be called from the Apps Script editor for troubleshooting
 */
function refreshSystem() {
  try {
    updateQueueColors();
    logAction('System', 'Manual Refresh', '', 'Admin', 'System refreshed manually');
    console.log('System refreshed successfully');
  } catch (error) {
    console.error('Error refreshing system:', error);
  }
}

/**
 * AUTOMATED SETUP FUNCTION - Run this once to set up everything!
 * This function creates all sheets, headers, formatting, and initial data
 */
function setupFloatTrackerSystem() {
  try {
    console.log('🚀 Starting automated Float Tracker setup...');

    const ss = SpreadsheetApp.getActiveSpreadsheet();

    // Step 1: Create and set up all required sheets
    setupAllSheets(ss);

    // Step 2: Add all headers and initial data
    setupSheetData(ss);

    // Step 3: Apply formatting and styling
    formatAllSheets(ss);

    // Step 4: Set up protection and hide sensitive sheets
    setupSheetProtection(ss);

    // Step 5: Initialize system with sample data
    setupInitialData(ss);

    // Step 6: Update colors and final touches
    updateQueueColors();

    console.log('✅ Float Tracker System setup complete!');

    // Show success message with instructions
    const ui = SpreadsheetApp.getUi();
    ui.alert(
      'Float Tracker Setup Complete! 🎉',
      'Your Float Tracker System is ready!\n\n' +
      'Next steps:\n' +
      '1. Create FLOAT and Admin Panel buttons (see instructions in column G)\n' +
      '2. Test with Sample User (PIN: 1234)\n' +
      '3. Add your real staff members via Admin Panel\n\n' +
      'Check the Float Queue sheet to get started!',
      ui.ButtonSet.OK
    );

  } catch (error) {
    console.error('❌ Setup failed:', error);
    SpreadsheetApp.getUi().alert('Setup Error', 'Setup failed: ' + error.message, SpreadsheetApp.getUi().ButtonSet.OK);
  }
}

/**
 * Create all required sheets with proper names
 */
function setupAllSheets(ss) {
  console.log('📋 Creating sheets...');

  // Get existing sheets
  const existingSheets = ss.getSheets().map(sheet => sheet.getName());

  // Create Float Queue sheet (rename Sheet1 if it exists)
  if (existingSheets.includes('Sheet1')) {
    ss.getSheetByName('Sheet1').setName(SHEET_NAMES.QUEUE);
  } else if (!existingSheets.includes(SHEET_NAMES.QUEUE)) {
    ss.insertSheet(SHEET_NAMES.QUEUE);
  }

  // Create other required sheets
  const requiredSheets = [SHEET_NAMES.LOG, SHEET_NAMES.USERDATA, SHEET_NAMES.SETTINGS];

  for (const sheetName of requiredSheets) {
    if (!existingSheets.includes(sheetName)) {
      ss.insertSheet(sheetName);
      console.log(`✅ Created sheet: ${sheetName}`);
    }
  }
}

/**
 * Set up all sheet headers and initial structure
 */
function setupSheetData(ss) {
  console.log('📝 Setting up sheet data...');

  // Float Queue sheet
  const queueSheet = ss.getSheetByName(SHEET_NAMES.QUEUE);
  queueSheet.getRange('A1:E1').setValues([
    ['Staff Name', 'Last Floated', 'Float Unit', 'Status', 'Hold Reason']
  ]);

  // Float Log sheet
  const logSheet = ss.getSheetByName(SHEET_NAMES.LOG);
  logSheet.getRange('A1:F1').setValues([
    ['Timestamp', 'Staff Name', 'Action', 'Unit', 'Admin', 'Notes']
  ]);

  // UserData sheet
  const userDataSheet = ss.getSheetByName(SHEET_NAMES.USERDATA);
  userDataSheet.getRange('A1:D1').setValues([
    ['Staff Name', 'PIN Hash', 'Created Date', 'Last Login']
  ]);

  // Settings sheet
  const settingsSheet = ss.getSheetByName(SHEET_NAMES.SETTINGS);
  settingsSheet.getRange('A1:B6').setValues([
    ['Setting', 'Value'],
    ['ADMIN_PASSWORD', ADMIN_PASSWORD_DEFAULT],
    ['SYSTEM_VERSION', VERSION],
    ['LAST_BACKUP', new Date()],
    ['TOTAL_FLOATS', 0],
    ['SYSTEM_CREATED', new Date()]
  ]);

  console.log('✅ Sheet data configured');
}

/**
 * Apply formatting to all sheets
 */
function formatAllSheets(ss) {
  console.log('🎨 Applying formatting...');

  // Format Float Queue sheet
  const queueSheet = ss.getSheetByName(SHEET_NAMES.QUEUE);

  // Header formatting
  const queueHeader = queueSheet.getRange('A1:E1');
  queueHeader.setFontWeight('bold');
  queueHeader.setBackground('#4285f4');
  queueHeader.setFontColor('#ffffff');
  queueHeader.setHorizontalAlignment('center');

  // Column widths
  queueSheet.setColumnWidth(1, 150); // Staff Name
  queueSheet.setColumnWidth(2, 120); // Last Floated
  queueSheet.setColumnWidth(3, 100); // Float Unit
  queueSheet.setColumnWidth(4, 80);  // Status
  queueSheet.setColumnWidth(5, 120); // Hold Reason

  // Format Float Log sheet
  const logSheet = ss.getSheetByName(SHEET_NAMES.LOG);

  // Header formatting
  const logHeader = logSheet.getRange('A1:F1');
  logHeader.setFontWeight('bold');
  logHeader.setBackground('#ea4335');
  logHeader.setFontColor('#ffffff');
  logHeader.setHorizontalAlignment('center');

  // Column widths
  logSheet.setColumnWidth(1, 130); // Timestamp
  logSheet.setColumnWidth(2, 120); // Staff Name
  logSheet.setColumnWidth(3, 120); // Action
  logSheet.setColumnWidth(4, 100); // Unit
  logSheet.setColumnWidth(5, 100); // Admin
  logSheet.setColumnWidth(6, 200); // Notes

  // Add button instructions to Float Queue sheet
  queueSheet.getRange('G1').setValue('BUTTON SETUP INSTRUCTIONS');
  queueSheet.getRange('G1').setFontWeight('bold');
  queueSheet.getRange('G1').setBackground('#f1f3f4');

  const instructions =
    'TO CREATE BUTTONS:\n\n' +
    '1. FLOAT Button (G3):\n' +
    '   - Insert > Drawing\n' +
    '   - Text: "FLOAT"\n' +
    '   - Blue background (#4285f4)\n' +
    '   - Assign script: showFloatDialog\n\n' +
    '2. Admin Panel Button (G5):\n' +
    '   - Insert > Drawing\n' +
    '   - Text: "Admin Panel"\n' +
    '   - Red background (#ea4335)\n' +
    '   - Assign script: showAdminPanel\n\n' +
    'Click buttons after creating to assign scripts!';

  queueSheet.getRange('G2').setValue(instructions);
  queueSheet.getRange('G2').setWrap(true);
  queueSheet.setRowHeight(2, 200);
  queueSheet.setColumnWidth(7, 250);

  console.log('✅ Formatting applied');
}

/**
 * Set up sheet protection and hide sensitive sheets
 */
function setupSheetProtection(ss) {
  console.log('🔒 Setting up protection...');

  // Hide and protect UserData sheet
  const userDataSheet = ss.getSheetByName(SHEET_NAMES.USERDATA);
  userDataSheet.hideSheet();
  const userDataProtection = userDataSheet.protect();
  userDataProtection.setDescription('User authentication data - Admin only');
  userDataProtection.setWarningOnly(false);

  // Hide and protect Settings sheet
  const settingsSheet = ss.getSheetByName(SHEET_NAMES.SETTINGS);
  settingsSheet.hideSheet();
  const settingsProtection = settingsSheet.protect();
  settingsProtection.setDescription('System settings - Admin only');
  settingsProtection.setWarningOnly(false);

  console.log('✅ Protection configured');
}
