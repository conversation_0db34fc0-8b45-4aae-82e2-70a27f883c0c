/**
 * Float Tracker System - Queue Manager Module
 * Google Sheets + Apps Script Implementation
 * 
 * Handles queue operations, ordering, and queue-related utilities.
 * Manages the fair rotation system and queue integrity.
 */

/**
 * Get the current queue order
 * @return {Array} Array of staff objects in queue order
 */
function getCurrentQueue() {
  try {
    console.log('Getting current queue order');
    
    const queueSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.QUEUE);
    const queueData = queueSheet.getDataRange().getValues();
    
    const queue = [];
    
    for (let i = 1; i < queueData.length; i++) { // Skip header row
      queue.push({
        staffName: queueData[i][QUEUE_COLUMNS.STAFF_NAME],
        lastFloated: queueData[i][QUEUE_COLUMNS.LAST_FLOATED],
        floatUnit: queueData[i][QUEUE_COLUMNS.FLOAT_UNIT],
        status: queueData[i][QUEUE_COLUMNS.STATUS],
        holdReason: queueData[i][QUEUE_COLUMNS.HOLD_REASON],
        position: i // 1-based position in queue
      });
    }
    
    return queue;
    
  } catch (error) {
    console.error('Error getting current queue:', error);
    return [];
  }
}

/**
 * Get the next staff member to float (first active person in queue)
 * @return {Object|null} Staff object or null if no one available
 */
function getNextToFloat() {
  try {
    console.log('Getting next staff member to float');
    
    const queue = getCurrentQueue();
    
    for (const staff of queue) {
      if (staff.status === 'Active') {
        return staff;
      }
    }
    
    console.log('No active staff members found in queue');
    return null;
    
  } catch (error) {
    console.error('Error getting next to float:', error);
    return null;
  }
}

/**
 * Get all active staff members in queue
 * @return {Array} Array of active staff objects
 */
function getActiveStaff() {
  try {
    console.log('Getting all active staff members');
    
    const queue = getCurrentQueue();
    
    return queue.filter(staff => staff.status === 'Active');
    
  } catch (error) {
    console.error('Error getting active staff:', error);
    return [];
  }
}

/**
 * Get all staff members on hold
 * @return {Array} Array of staff objects on hold
 */
function getStaffOnHold() {
  try {
    console.log('Getting staff members on hold');
    
    const queue = getCurrentQueue();
    
    return queue.filter(staff => staff.status !== 'Active');
    
  } catch (error) {
    console.error('Error getting staff on hold:', error);
    return [];
  }
}

/**
 * Move a staff member to a specific position in the queue
 * @param {string} staffName - Name of staff member to move
 * @param {number} newPosition - New position (1-based)
 * @return {Object} Result object with success status
 */
function moveStaffToPosition(staffName, newPosition) {
  try {
    console.log(`Moving ${staffName} to position ${newPosition}`);
    
    const queueSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.QUEUE);
    const data = queueSheet.getDataRange().getValues();
    
    if (newPosition < 1 || newPosition > data.length - 1) {
      return { success: false, message: 'Invalid position specified.' };
    }
    
    // Find the staff member's current row
    let staffRowIndex = -1;
    let staffRow = null;
    
    for (let i = 1; i < data.length; i++) { // Skip header row
      if (data[i][QUEUE_COLUMNS.STAFF_NAME] === staffName) {
        staffRowIndex = i;
        staffRow = data[i];
        break;
      }
    }
    
    if (staffRowIndex === -1) {
      return { success: false, message: 'Staff member not found in queue.' };
    }
    
    // Remove staff from current position
    data.splice(staffRowIndex, 1);
    
    // Insert staff at new position
    data.splice(newPosition, 0, staffRow);
    
    // Update the sheet
    queueSheet.clear();
    queueSheet.getRange(1, 1, data.length, data[0].length).setValues(data);
    
    // Restore header formatting
    const headerRange = queueSheet.getRange(1, 1, 1, data[0].length);
    headerRange.setFontWeight('bold');
    headerRange.setBackground('#4285f4');
    headerRange.setFontColor('#ffffff');
    
    // Update queue colors
    updateQueueColors();
    
    // Log the action
    logAction(staffName, 'Queue Position Changed', '', 'Admin', `Moved to position ${newPosition}`);
    
    return { 
      success: true, 
      message: `${staffName} moved to position ${newPosition} in queue.` 
    };
    
  } catch (error) {
    console.error('Error moving staff to position:', error);
    return { success: false, message: 'Failed to move staff: ' + error.message };
  }
}

/**
 * Swap positions of two staff members in the queue
 * @param {string} staffName1 - First staff member
 * @param {string} staffName2 - Second staff member
 * @return {Object} Result object with success status
 */
function swapStaffPositions(staffName1, staffName2) {
  try {
    console.log(`Swapping positions of ${staffName1} and ${staffName2}`);
    
    const queueSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.QUEUE);
    const data = queueSheet.getDataRange().getValues();
    
    // Find both staff members
    let staff1Index = -1;
    let staff2Index = -1;
    
    for (let i = 1; i < data.length; i++) { // Skip header row
      if (data[i][QUEUE_COLUMNS.STAFF_NAME] === staffName1) {
        staff1Index = i;
      }
      if (data[i][QUEUE_COLUMNS.STAFF_NAME] === staffName2) {
        staff2Index = i;
      }
    }
    
    if (staff1Index === -1) {
      return { success: false, message: `Staff member "${staffName1}" not found in queue.` };
    }
    
    if (staff2Index === -1) {
      return { success: false, message: `Staff member "${staffName2}" not found in queue.` };
    }
    
    // Swap the rows
    const temp = data[staff1Index];
    data[staff1Index] = data[staff2Index];
    data[staff2Index] = temp;
    
    // Update the sheet
    queueSheet.clear();
    queueSheet.getRange(1, 1, data.length, data[0].length).setValues(data);
    
    // Restore header formatting
    const headerRange = queueSheet.getRange(1, 1, 1, data[0].length);
    headerRange.setFontWeight('bold');
    headerRange.setBackground('#4285f4');
    headerRange.setFontColor('#ffffff');
    
    // Update queue colors
    updateQueueColors();
    
    // Log the action
    logAction('Queue', 'Positions Swapped', '', 'Admin', `${staffName1} ↔ ${staffName2}`);
    
    return { 
      success: true, 
      message: `Positions swapped: ${staffName1} ↔ ${staffName2}` 
    };
    
  } catch (error) {
    console.error('Error swapping staff positions:', error);
    return { success: false, message: 'Failed to swap positions: ' + error.message };
  }
}

/**
 * Sort queue by last floated date (oldest first)
 * @return {Object} Result object with success status
 */
function sortQueueByLastFloated() {
  try {
    console.log('Sorting queue by last floated date');
    
    const queueSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.QUEUE);
    const data = queueSheet.getDataRange().getValues();
    
    if (data.length <= 1) {
      return { success: false, message: 'No staff members in queue to sort.' };
    }
    
    // Separate header and data rows
    const header = data[0];
    const dataRows = data.slice(1);
    
    // Sort by last floated date (oldest first, empty dates first)
    dataRows.sort((a, b) => {
      const dateA = a[QUEUE_COLUMNS.LAST_FLOATED];
      const dateB = b[QUEUE_COLUMNS.LAST_FLOATED];
      
      // Empty dates go first (never floated)
      if (!dateA && !dateB) return 0;
      if (!dateA) return -1;
      if (!dateB) return 1;
      
      // Compare actual dates
      return new Date(dateA) - new Date(dateB);
    });
    
    // Combine header and sorted data
    const newData = [header, ...dataRows];
    
    // Update the sheet
    queueSheet.clear();
    queueSheet.getRange(1, 1, newData.length, newData[0].length).setValues(newData);
    
    // Restore header formatting
    const headerRange = queueSheet.getRange(1, 1, 1, newData[0].length);
    headerRange.setFontWeight('bold');
    headerRange.setBackground('#4285f4');
    headerRange.setFontColor('#ffffff');
    
    // Update queue colors
    updateQueueColors();
    
    // Log the action
    logAction('Queue', 'Queue Sorted', '', 'Admin', 'Sorted by last floated date');
    
    return { 
      success: true, 
      message: 'Queue sorted by last floated date (oldest first).' 
    };
    
  } catch (error) {
    console.error('Error sorting queue:', error);
    return { success: false, message: 'Failed to sort queue: ' + error.message };
  }
}

/**
 * Get queue statistics
 * @return {Object} Statistics about the current queue
 */
function getQueueStatistics() {
  try {
    console.log('Generating queue statistics');
    
    const queue = getCurrentQueue();
    
    const stats = {
      totalStaff: queue.length,
      activeStaff: 0,
      staffOnHold: 0,
      neverFloated: 0,
      averageDaysSinceFloat: 0,
      nextToFloat: null,
      longestWaitingStaff: null,
      longestWaitingDays: 0
    };
    
    let totalDaysSinceFloat = 0;
    let staffWithFloatDates = 0;
    const currentDate = new Date();
    
    for (const staff of queue) {
      if (staff.status === 'Active') {
        stats.activeStaff++;
        
        // Check if this is next to float
        if (!stats.nextToFloat) {
          stats.nextToFloat = staff.staffName;
        }
      } else {
        stats.staffOnHold++;
      }
      
      // Check float history
      if (!staff.lastFloated || staff.lastFloated === '') {
        stats.neverFloated++;
      } else {
        const lastFloatedDate = new Date(staff.lastFloated);
        const daysSince = Math.floor((currentDate - lastFloatedDate) / (24 * 60 * 60 * 1000));
        
        totalDaysSinceFloat += daysSince;
        staffWithFloatDates++;
        
        // Track longest waiting
        if (daysSince > stats.longestWaitingDays) {
          stats.longestWaitingDays = daysSince;
          stats.longestWaitingStaff = staff.staffName;
        }
      }
    }
    
    // Calculate average
    if (staffWithFloatDates > 0) {
      stats.averageDaysSinceFloat = Math.round(totalDaysSinceFloat / staffWithFloatDates);
    }
    
    return stats;
    
  } catch (error) {
    console.error('Error generating queue statistics:', error);
    return null;
  }
}

/**
 * Validate queue integrity and fix any issues
 * @return {Object} Result object with validation results
 */
function validateAndFixQueue() {
  try {
    console.log('Validating queue integrity');
    
    const queueSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.QUEUE);
    const userDataSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.USERDATA);
    
    const queueData = queueSheet.getDataRange().getValues();
    const userData = userDataSheet.getDataRange().getValues();
    
    const issues = [];
    const fixes = [];
    
    // Get list of valid users from UserData sheet
    const validUsers = [];
    for (let i = 1; i < userData.length; i++) { // Skip header row
      validUsers.push(userData[i][0]); // Staff Name column
    }
    
    // Check for users in queue who don't exist in UserData
    for (let i = 1; i < queueData.length; i++) { // Skip header row
      const staffName = queueData[i][QUEUE_COLUMNS.STAFF_NAME];
      
      if (!validUsers.includes(staffName)) {
        issues.push(`User "${staffName}" in queue but not in UserData`);
        // Remove from queue
        queueSheet.deleteRow(i + 1);
        fixes.push(`Removed "${staffName}" from queue`);
      }
    }
    
    // Check for users in UserData who don't exist in queue
    for (const userName of validUsers) {
      let foundInQueue = false;
      
      for (let i = 1; i < queueData.length; i++) { // Skip header row
        if (queueData[i][QUEUE_COLUMNS.STAFF_NAME] === userName) {
          foundInQueue = true;
          break;
        }
      }
      
      if (!foundInQueue) {
        issues.push(`User "${userName}" in UserData but not in queue`);
        // Add to queue
        queueSheet.appendRow([
          userName,
          '', // Last Floated
          '', // Float Unit
          'Active', // Status
          '' // Hold Reason
        ]);
        fixes.push(`Added "${userName}" to queue`);
      }
    }
    
    // Update queue colors after fixes
    if (fixes.length > 0) {
      updateQueueColors();
    }
    
    // Log validation results
    if (issues.length > 0) {
      logAction('System', 'Queue Validation', '', 'System', `Found ${issues.length} issues, applied ${fixes.length} fixes`);
    }
    
    return {
      success: true,
      issuesFound: issues.length,
      fixesApplied: fixes.length,
      issues: issues,
      fixes: fixes
    };
    
  } catch (error) {
    console.error('Error validating queue:', error);
    return {
      success: false,
      message: 'Failed to validate queue: ' + error.message
    };
  }
}
