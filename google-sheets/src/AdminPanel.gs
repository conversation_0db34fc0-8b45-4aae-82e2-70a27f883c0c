/**
 * Float Tracker System - Admin Panel Module
 * Google Sheets + Apps Script Implementation
 * 
 * Handles all administrative functions including user management,
 * queue control, and system overrides.
 */

/**
 * Show the Admin Panel dialog when Admin Panel button is clicked
 * This function should be assigned to the Admin Panel button
 */
function showAdminPanel() {
  try {
    console.log('Admin panel requested');
    
    // Create HTML dialog for admin authentication and menu
    const htmlOutput = HtmlService.createHtmlOutput(`
      <!DOCTYPE html>
      <html>
        <head>
          <base target="_top">
          <style>
            body { font-family: Arial, sans-serif; padding: 20px; }
            .form-group { margin-bottom: 15px; }
            label { display: block; margin-bottom: 5px; font-weight: bold; }
            input, select, textarea { width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; }
            button { background-color: #ea4335; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px; }
            button:hover { background-color: #d33b2c; }
            .success { background-color: #34a853; }
            .success:hover { background-color: #2d8f47; }
            .cancel { background-color: #9aa0a6; }
            .cancel:hover { background-color: #80868b; }
            .admin-menu { display: none; }
            .menu-item { margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 4px; cursor: pointer; }
            .menu-item:hover { background-color: #f1f3f4; }
            .result { margin-top: 15px; padding: 10px; border-radius: 4px; }
            .result.success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
            .result.error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
          </style>
        </head>
        <body>
          <div id="loginForm">
            <h3>Admin Panel Access</h3>
            <div class="form-group">
              <label for="adminPassword">Admin Password:</label>
              <input type="password" id="adminPassword" name="adminPassword" required>
            </div>
            <div class="form-group">
              <label for="adminName">Your Name (for logging):</label>
              <input type="text" id="adminName" name="adminName" required>
            </div>
            <button type="button" onclick="authenticateAdmin()">Access Admin Panel</button>
            <button type="button" class="cancel" onclick="google.script.host.close()">Cancel</button>
          </div>
          
          <div id="adminMenu" class="admin-menu">
            <h3>Admin Panel - Welcome <span id="welcomeName"></span></h3>
            <div class="menu-item" onclick="showOverrideFloat()">1 - Override Float (Float someone without PIN)</div>
            <div class="menu-item" onclick="showAddUser()">2 - Add New User</div>
            <div class="menu-item" onclick="showRemoveUser()">3 - Remove User</div>
            <div class="menu-item" onclick="showResetPIN()">4 - Reset User PIN</div>
            <div class="menu-item" onclick="showHoldStaff()">5 - Hold Staff (Vacation/Unavailable)</div>
            <div class="menu-item" onclick="showReleaseStaff()">6 - Release Staff from Hold</div>
            <div class="menu-item" onclick="showResetQueue()">7 - Reset Queue Order</div>
            <div class="menu-item" onclick="showFloatSummary()">8 - View Float Summary</div>
            <div class="menu-item" onclick="showMissedFloats()">9 - View Missed Floats</div>
            <div class="menu-item" onclick="google.script.host.close()">0 - Exit Admin Panel</div>
          </div>
          
          <div id="actionForm" style="display: none;">
            <div id="actionContent"></div>
            <div id="actionResult"></div>
          </div>
          
          <script>
            let currentAdmin = '';
            
            function authenticateAdmin() {
              const password = document.getElementById('adminPassword').value;
              const adminName = document.getElementById('adminName').value;
              
              if (!password || !adminName) {
                alert('Please enter both password and your name.');
                return;
              }
              
              google.script.run
                .withSuccessHandler(onAuthSuccess)
                .withFailureHandler(onAuthFailure)
                .authenticateAdmin(password, adminName);
            }
            
            function onAuthSuccess(result) {
              if (result.success) {
                currentAdmin = result.adminName;
                document.getElementById('welcomeName').textContent = currentAdmin;
                document.getElementById('loginForm').style.display = 'none';
                document.getElementById('adminMenu').style.display = 'block';
              } else {
                alert('Authentication failed: ' + result.message);
              }
            }
            
            function onAuthFailure(error) {
              alert('Authentication error: ' + error.message);
            }
            
            function showAddUser() {
              showActionForm(\`
                <h4>Add New User</h4>
                <div class="form-group">
                  <label for="newUserName">Staff Member Name:</label>
                  <input type="text" id="newUserName" placeholder="Enter full name">
                </div>
                <button onclick="addUser()" class="success">Add User</button>
                <button onclick="backToMenu()" class="cancel">Back to Menu</button>
              \`);
            }
            
            function addUser() {
              const userName = document.getElementById('newUserName').value;
              if (!userName) {
                showResult('Please enter a staff member name.', 'error');
                return;
              }
              
              google.script.run
                .withSuccessHandler(onAddUserSuccess)
                .withFailureHandler(onActionFailure)
                .adminAddUser(userName, currentAdmin);
            }
            
            function onAddUserSuccess(result) {
              if (result.success) {
                showResult(\`User "\${result.staffName}" added successfully!\\n\\nGenerated PIN: \${result.pin}\\n\\n⚠️ IMPORTANT: Write down this PIN and give it to the staff member privately. This PIN will not be shown again!\`, 'success');
              } else {
                showResult('Error: ' + result.message, 'error');
              }
            }
            
            function showRemoveUser() {
              showActionForm(\`
                <h4>Remove User</h4>
                <div class="form-group">
                  <label for="removeUserName">Staff Member Name:</label>
                  <input type="text" id="removeUserName" placeholder="Enter exact name">
                </div>
                <button onclick="removeUser()" class="success">Remove User</button>
                <button onclick="backToMenu()" class="cancel">Back to Menu</button>
              \`);
            }
            
            function removeUser() {
              const userName = document.getElementById('removeUserName').value;
              if (!userName) {
                showResult('Please enter a staff member name.', 'error');
                return;
              }
              
              if (!confirm(\`Are you sure you want to remove "\${userName}" from the system? This action cannot be undone.\`)) {
                return;
              }
              
              google.script.run
                .withSuccessHandler(onActionSuccess)
                .withFailureHandler(onActionFailure)
                .adminRemoveUser(userName, currentAdmin);
            }
            
            function showResetPIN() {
              showActionForm(\`
                <h4>Reset User PIN</h4>
                <div class="form-group">
                  <label for="resetPINUserName">Staff Member Name:</label>
                  <input type="text" id="resetPINUserName" placeholder="Enter exact name">
                </div>
                <button onclick="resetPIN()" class="success">Reset PIN</button>
                <button onclick="backToMenu()" class="cancel">Back to Menu</button>
              \`);
            }
            
            function resetPIN() {
              const userName = document.getElementById('resetPINUserName').value;
              if (!userName) {
                showResult('Please enter a staff member name.', 'error');
                return;
              }
              
              google.script.run
                .withSuccessHandler(onResetPINSuccess)
                .withFailureHandler(onActionFailure)
                .adminResetPIN(userName, currentAdmin);
            }
            
            function onResetPINSuccess(result) {
              if (result.success) {
                showResult(\`PIN reset successfully for "\${result.staffName}"!\\n\\nNew PIN: \${result.pin}\\n\\n⚠️ IMPORTANT: Give this new PIN to the staff member privately.\`, 'success');
              } else {
                showResult('Error: ' + result.message, 'error');
              }
            }
            
            function showHoldStaff() {
              showActionForm(\`
                <h4>Hold Staff Member</h4>
                <div class="form-group">
                  <label for="holdUserName">Staff Member Name:</label>
                  <input type="text" id="holdUserName" placeholder="Enter exact name">
                </div>
                <div class="form-group">
                  <label for="holdReason">Reason for Hold:</label>
                  <input type="text" id="holdReason" placeholder="e.g., Vacation, Training, etc.">
                </div>
                <button onclick="holdStaff()" class="success">Hold Staff</button>
                <button onclick="backToMenu()" class="cancel">Back to Menu</button>
              \`);
            }
            
            function holdStaff() {
              const userName = document.getElementById('holdUserName').value;
              const reason = document.getElementById('holdReason').value;
              
              if (!userName || !reason) {
                showResult('Please enter both staff name and reason for hold.', 'error');
                return;
              }
              
              google.script.run
                .withSuccessHandler(onActionSuccess)
                .withFailureHandler(onActionFailure)
                .adminHoldStaff(userName, reason, currentAdmin);
            }
            
            function showReleaseStaff() {
              showActionForm(\`
                <h4>Release Staff from Hold</h4>
                <div class="form-group">
                  <label for="releaseUserName">Staff Member Name:</label>
                  <input type="text" id="releaseUserName" placeholder="Enter exact name">
                </div>
                <button onclick="releaseStaff()" class="success">Release Staff</button>
                <button onclick="backToMenu()" class="cancel">Back to Menu</button>
              \`);
            }
            
            function releaseStaff() {
              const userName = document.getElementById('releaseUserName').value;
              if (!userName) {
                showResult('Please enter a staff member name.', 'error');
                return;
              }
              
              google.script.run
                .withSuccessHandler(onActionSuccess)
                .withFailureHandler(onActionFailure)
                .adminReleaseStaff(userName, currentAdmin);
            }
            
            function showOverrideFloat() {
              showActionForm(\`
                <h4>Override Float</h4>
                <div class="form-group">
                  <label for="overrideUserName">Staff Member Name:</label>
                  <input type="text" id="overrideUserName" placeholder="Enter exact name">
                </div>
                <div class="form-group">
                  <label for="overrideUnit">Destination Unit:</label>
                  <select id="overrideUnit">
                    <option value="">-- Select Unit --</option>
                    <option value="ICU">ICU</option>
                    <option value="ER">Emergency Room</option>
                    <option value="Med/Surg">Medical/Surgical</option>
                    <option value="Pediatrics">Pediatrics</option>
                    <option value="Maternity">Maternity</option>
                    <option value="OR">Operating Room</option>
                    <option value="Recovery">Recovery</option>
                    <option value="Oncology">Oncology</option>
                    <option value="Cardiology">Cardiology</option>
                    <option value="Other">Other</option>
                  </select>
                </div>
                <button onclick="overrideFloat()" class="success">Override Float</button>
                <button onclick="backToMenu()" class="cancel">Back to Menu</button>
              \`);
            }
            
            function overrideFloat() {
              const userName = document.getElementById('overrideUserName').value;
              const unit = document.getElementById('overrideUnit').value;
              
              if (!userName || !unit) {
                showResult('Please enter both staff name and destination unit.', 'error');
                return;
              }
              
              google.script.run
                .withSuccessHandler(onActionSuccess)
                .withFailureHandler(onActionFailure)
                .adminOverrideFloat(userName, unit, currentAdmin);
            }
            
            function showResetQueue() {
              showActionForm(\`
                <h4>Reset Queue Order</h4>
                <p>This will randomize the order of all active staff members in the queue.</p>
                <button onclick="resetQueue()" class="success">Reset Queue Order</button>
                <button onclick="backToMenu()" class="cancel">Back to Menu</button>
              \`);
            }
            
            function resetQueue() {
              if (!confirm('Are you sure you want to reset the queue order? This will randomize all active staff positions.')) {
                return;
              }
              
              google.script.run
                .withSuccessHandler(onActionSuccess)
                .withFailureHandler(onActionFailure)
                .adminResetQueue(currentAdmin);
            }
            
            function showFloatSummary() {
              google.script.run
                .withSuccessHandler(onReportSuccess)
                .withFailureHandler(onActionFailure)
                .getFloatSummary();
            }
            
            function showMissedFloats() {
              google.script.run
                .withSuccessHandler(onReportSuccess)
                .withFailureHandler(onActionFailure)
                .getMissedFloats();
            }
            
            function onReportSuccess(result) {
              showActionForm(\`
                <h4>\${result.title}</h4>
                <pre style="background-color: #f8f9fa; padding: 15px; border-radius: 4px; white-space: pre-wrap;">\${result.content}</pre>
                <button onclick="backToMenu()" class="cancel">Back to Menu</button>
              \`);
            }
            
            function onActionSuccess(result) {
              if (result.success) {
                showResult(result.message, 'success');
              } else {
                showResult('Error: ' + result.message, 'error');
              }
            }
            
            function onActionFailure(error) {
              showResult('System error: ' + error.message, 'error');
            }
            
            function showActionForm(content) {
              document.getElementById('adminMenu').style.display = 'none';
              document.getElementById('actionContent').innerHTML = content;
              document.getElementById('actionResult').innerHTML = '';
              document.getElementById('actionForm').style.display = 'block';
            }
            
            function backToMenu() {
              document.getElementById('actionForm').style.display = 'none';
              document.getElementById('adminMenu').style.display = 'block';
            }
            
            function showResult(message, type) {
              const resultDiv = document.getElementById('actionResult');
              resultDiv.innerHTML = \`<div class="result \${type}">\${message}</div>\`;
            }
            
            // Auto-focus on password field
            document.getElementById('adminPassword').focus();
          </script>
        </body>
      </html>
    `).setWidth(600).setHeight(500);
    
    SpreadsheetApp.getUi().showModalDialog(htmlOutput, 'Admin Panel');
    
  } catch (error) {
    console.error('Error showing admin panel:', error);
    SpreadsheetApp.getUi().alert('Error: ' + error.message);
  }
}

/**
 * Authenticate admin access
 * @param {string} password - The admin password
 * @param {string} adminName - The admin's name for logging
 * @return {Object} Result object with success status
 */
function authenticateAdmin(password, adminName) {
  try {
    console.log(`Admin authentication attempt by: ${adminName}`);

    if (!validateAdminPassword(password)) {
      logAction('Admin', 'Failed Login Attempt', '', adminName, 'Invalid password');
      return { success: false, message: 'Invalid admin password.' };
    }

    logAction('Admin', 'Admin Login', '', adminName, 'Admin panel accessed');

    return {
      success: true,
      message: 'Authentication successful.',
      adminName: adminName
    };

  } catch (error) {
    console.error('Error authenticating admin:', error);
    return { success: false, message: 'Authentication error: ' + error.message };
  }
}

/**
 * Admin function to add a new user
 * @param {string} staffName - Name of staff member to add
 * @param {string} adminName - Name of admin performing action
 * @return {Object} Result object with success status and PIN
 */
function adminAddUser(staffName, adminName) {
  try {
    const result = addNewUser(staffName);

    if (result.success) {
      logAction(staffName, 'User Added', '', adminName, 'Added via admin panel');
    }

    return result;

  } catch (error) {
    console.error('Error in admin add user:', error);
    return { success: false, message: 'Failed to add user: ' + error.message };
  }
}

/**
 * Admin function to remove a user
 * @param {string} staffName - Name of staff member to remove
 * @param {string} adminName - Name of admin performing action
 * @return {Object} Result object with success status
 */
function adminRemoveUser(staffName, adminName) {
  try {
    const result = removeUser(staffName);

    if (result.success) {
      logAction(staffName, 'User Removed', '', adminName, 'Removed via admin panel');
    }

    return result;

  } catch (error) {
    console.error('Error in admin remove user:', error);
    return { success: false, message: 'Failed to remove user: ' + error.message };
  }
}

/**
 * Admin function to reset a user's PIN
 * @param {string} staffName - Name of staff member
 * @param {string} adminName - Name of admin performing action
 * @return {Object} Result object with success status and new PIN
 */
function adminResetPIN(staffName, adminName) {
  try {
    const result = resetUserPIN(staffName);

    if (result.success) {
      logAction(staffName, 'PIN Reset', '', adminName, 'PIN reset via admin panel');
    }

    return result;

  } catch (error) {
    console.error('Error in admin reset PIN:', error);
    return { success: false, message: 'Failed to reset PIN: ' + error.message };
  }
}

/**
 * Admin function to hold a staff member
 * @param {string} staffName - Name of staff member to hold
 * @param {string} reason - Reason for the hold
 * @param {string} adminName - Name of admin performing action
 * @return {Object} Result object with success status
 */
function adminHoldStaff(staffName, reason, adminName) {
  try {
    console.log(`Admin holding staff: ${staffName}, reason: ${reason}`);

    if (!staffName || !reason) {
      return { success: false, message: 'Staff name and reason are required.' };
    }

    // Find user in queue and update status
    const queueSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.QUEUE);
    const queueData = queueSheet.getDataRange().getValues();

    let userFound = false;
    for (let i = 1; i < queueData.length; i++) { // Skip header row
      if (queueData[i][QUEUE_COLUMNS.STAFF_NAME] === staffName) {
        queueSheet.getRange(i + 1, QUEUE_COLUMNS.STATUS + 1).setValue('Hold');
        queueSheet.getRange(i + 1, QUEUE_COLUMNS.HOLD_REASON + 1).setValue(reason);
        userFound = true;
        break;
      }
    }

    if (!userFound) {
      return { success: false, message: 'Staff member not found in queue.' };
    }

    // Update queue colors
    updateQueueColors();

    // Log the action
    logAction(staffName, 'Staff Held', '', adminName, `Hold reason: ${reason}`);

    return {
      success: true,
      message: `Staff member "${staffName}" has been placed on hold.\nReason: ${reason}`
    };

  } catch (error) {
    console.error('Error holding staff:', error);
    return { success: false, message: 'Failed to hold staff: ' + error.message };
  }
}

/**
 * Admin function to release a staff member from hold
 * @param {string} staffName - Name of staff member to release
 * @param {string} adminName - Name of admin performing action
 * @return {Object} Result object with success status
 */
function adminReleaseStaff(staffName, adminName) {
  try {
    console.log(`Admin releasing staff from hold: ${staffName}`);

    if (!staffName) {
      return { success: false, message: 'Staff name is required.' };
    }

    // Find user in queue and update status
    const queueSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.QUEUE);
    const queueData = queueSheet.getDataRange().getValues();

    let userFound = false;
    for (let i = 1; i < queueData.length; i++) { // Skip header row
      if (queueData[i][QUEUE_COLUMNS.STAFF_NAME] === staffName) {
        queueSheet.getRange(i + 1, QUEUE_COLUMNS.STATUS + 1).setValue('Active');
        queueSheet.getRange(i + 1, QUEUE_COLUMNS.HOLD_REASON + 1).setValue('');
        userFound = true;
        break;
      }
    }

    if (!userFound) {
      return { success: false, message: 'Staff member not found in queue.' };
    }

    // Update queue colors
    updateQueueColors();

    // Log the action
    logAction(staffName, 'Staff Released', '', adminName, 'Released from hold');

    return {
      success: true,
      message: `Staff member "${staffName}" has been released from hold and is now active.`
    };

  } catch (error) {
    console.error('Error releasing staff:', error);
    return { success: false, message: 'Failed to release staff: ' + error.message };
  }
}

/**
 * Admin function to override a float (float someone without PIN)
 * @param {string} staffName - Name of staff member to float
 * @param {string} unit - Destination unit
 * @param {string} adminName - Name of admin performing action
 * @return {Object} Result object with success status
 */
function adminOverrideFloat(staffName, unit, adminName) {
  try {
    console.log(`Admin override float: ${staffName} to ${unit}`);

    if (!staffName || !unit) {
      return { success: false, message: 'Staff name and unit are required.' };
    }

    // Check if user exists in queue
    if (!userExists(staffName)) {
      return { success: false, message: 'Staff member not found in the system.' };
    }

    // Execute the float
    const result = executeFloat(staffName, unit);

    if (result.success) {
      // Update queue colors
      updateQueueColors();

      // Log the action
      logAction(staffName, 'Admin Override Float', unit, adminName, 'Float overridden by admin');

      return {
        success: true,
        message: `Override float processed successfully!\n\n${staffName} has been floated to ${unit} and moved to the bottom of the queue.`
      };
    } else {
      return result;
    }

  } catch (error) {
    console.error('Error in admin override float:', error);
    return { success: false, message: 'Failed to override float: ' + error.message };
  }
}

/**
 * Admin function to reset the queue order (randomize)
 * @param {string} adminName - Name of admin performing action
 * @return {Object} Result object with success status
 */
function adminResetQueue(adminName) {
  try {
    console.log('Admin resetting queue order');

    const queueSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.QUEUE);
    const data = queueSheet.getDataRange().getValues();

    if (data.length <= 1) {
      return { success: false, message: 'No staff members in queue to reorder.' };
    }

    // Separate header and data rows
    const header = data[0];
    const dataRows = data.slice(1);

    // Shuffle the data rows randomly
    for (let i = dataRows.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [dataRows[i], dataRows[j]] = [dataRows[j], dataRows[i]];
    }

    // Combine header and shuffled data
    const newData = [header, ...dataRows];

    // Clear sheet and write new data
    queueSheet.clear();
    queueSheet.getRange(1, 1, newData.length, newData[0].length).setValues(newData);

    // Restore header formatting
    const headerRange = queueSheet.getRange(1, 1, 1, newData[0].length);
    headerRange.setFontWeight('bold');
    headerRange.setBackground('#4285f4');
    headerRange.setFontColor('#ffffff');

    // Update queue colors
    updateQueueColors();

    // Log the action
    logAction('Queue', 'Queue Reset', '', adminName, 'Queue order randomized');

    return {
      success: true,
      message: `Queue order has been reset and randomized.\n\nAll active staff members have been reordered randomly.`
    };

  } catch (error) {
    console.error('Error resetting queue:', error);
    return { success: false, message: 'Failed to reset queue: ' + error.message };
  }
}

/**
 * Generate float summary report
 * @return {Object} Report object with title and content
 */
function getFloatSummary() {
  try {
    console.log('Generating float summary report');

    const logSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.LOG);
    const logData = logSheet.getDataRange().getValues();

    if (logData.length <= 1) {
      return {
        title: 'Float Summary Report',
        content: 'No float data available yet.'
      };
    }

    // Count floats by staff member
    const floatCounts = {};
    const recentFloats = [];

    for (let i = 1; i < logData.length; i++) { // Skip header row
      const timestamp = logData[i][LOG_COLUMNS.TIMESTAMP];
      const staffName = logData[i][LOG_COLUMNS.STAFF_NAME];
      const action = logData[i][LOG_COLUMNS.ACTION];
      const unit = logData[i][LOG_COLUMNS.UNIT];

      if (action === 'Float Submitted' || action === 'Admin Override Float') {
        // Count floats
        if (!floatCounts[staffName]) {
          floatCounts[staffName] = 0;
        }
        floatCounts[staffName]++;

        // Track recent floats (last 10)
        recentFloats.push({
          timestamp: timestamp,
          staffName: staffName,
          unit: unit,
          action: action
        });
      }
    }

    // Sort recent floats by timestamp (most recent first)
    recentFloats.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    // Build report content
    let content = 'FLOAT SUMMARY REPORT\n';
    content += '===================\n\n';

    content += 'FLOAT COUNTS BY STAFF MEMBER:\n';
    content += '-----------------------------\n';

    if (Object.keys(floatCounts).length === 0) {
      content += 'No floats recorded yet.\n\n';
    } else {
      // Sort by float count (descending)
      const sortedCounts = Object.entries(floatCounts).sort((a, b) => b[1] - a[1]);

      for (const [staffName, count] of sortedCounts) {
        content += `${staffName}: ${count} float${count !== 1 ? 's' : ''}\n`;
      }
      content += '\n';
    }

    content += 'RECENT FLOATS (Last 10):\n';
    content += '------------------------\n';

    if (recentFloats.length === 0) {
      content += 'No recent floats.\n';
    } else {
      const displayFloats = recentFloats.slice(0, 10);
      for (const float of displayFloats) {
        const date = new Date(float.timestamp).toLocaleString();
        content += `${date} - ${float.staffName} → ${float.unit}\n`;
      }
    }

    return {
      title: 'Float Summary Report',
      content: content
    };

  } catch (error) {
    console.error('Error generating float summary:', error);
    return {
      title: 'Float Summary Report',
      content: 'Error generating report: ' + error.message
    };
  }
}

/**
 * Generate missed floats report
 * @return {Object} Report object with title and content
 */
function getMissedFloats() {
  try {
    console.log('Generating missed floats report');

    const queueSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.QUEUE);
    const queueData = queueSheet.getDataRange().getValues();

    if (queueData.length <= 1) {
      return {
        title: 'Missed Floats Report',
        content: 'No staff members in queue.'
      };
    }

    // Analyze queue for missed float opportunities
    const currentTime = new Date();
    const oneDayAgo = new Date(currentTime.getTime() - (24 * 60 * 60 * 1000));
    const oneWeekAgo = new Date(currentTime.getTime() - (7 * 24 * 60 * 60 * 1000));

    let content = 'MISSED FLOATS REPORT\n';
    content += '===================\n\n';

    content += 'STAFF WHO HAVEN\'T FLOATED RECENTLY:\n';
    content += '-----------------------------------\n';

    const neverFloated = [];
    const notFloatedToday = [];
    const notFloatedThisWeek = [];

    for (let i = 1; i < queueData.length; i++) { // Skip header row
      const staffName = queueData[i][QUEUE_COLUMNS.STAFF_NAME];
      const lastFloated = queueData[i][QUEUE_COLUMNS.LAST_FLOATED];
      const status = queueData[i][QUEUE_COLUMNS.STATUS];

      if (status !== 'Active') {
        continue; // Skip staff on hold
      }

      if (!lastFloated || lastFloated === '') {
        neverFloated.push(staffName);
      } else {
        const lastFloatedDate = new Date(lastFloated);

        if (lastFloatedDate < oneDayAgo) {
          notFloatedToday.push({
            name: staffName,
            lastFloated: lastFloatedDate
          });
        }

        if (lastFloatedDate < oneWeekAgo) {
          notFloatedThisWeek.push({
            name: staffName,
            lastFloated: lastFloatedDate
          });
        }
      }
    }

    // Never floated
    if (neverFloated.length > 0) {
      content += 'NEVER FLOATED:\n';
      for (const name of neverFloated) {
        content += `• ${name}\n`;
      }
      content += '\n';
    }

    // Not floated today
    if (notFloatedToday.length > 0) {
      content += 'NOT FLOATED IN LAST 24 HOURS:\n';
      notFloatedToday.sort((a, b) => a.lastFloated - b.lastFloated); // Oldest first
      for (const staff of notFloatedToday) {
        const daysSince = Math.floor((currentTime - staff.lastFloated) / (24 * 60 * 60 * 1000));
        content += `• ${staff.name} (${daysSince} day${daysSince !== 1 ? 's' : ''} ago)\n`;
      }
      content += '\n';
    }

    // Not floated this week
    if (notFloatedThisWeek.length > 0) {
      content += 'NOT FLOATED IN LAST 7 DAYS:\n';
      notFloatedThisWeek.sort((a, b) => a.lastFloated - b.lastFloated); // Oldest first
      for (const staff of notFloatedThisWeek) {
        const daysSince = Math.floor((currentTime - staff.lastFloated) / (24 * 60 * 60 * 1000));
        content += `• ${staff.name} (${daysSince} day${daysSince !== 1 ? 's' : ''} ago)\n`;
      }
      content += '\n';
    }

    // Summary
    if (neverFloated.length === 0 && notFloatedToday.length === 0 && notFloatedThisWeek.length === 0) {
      content += 'All active staff members have floated recently. Great job maintaining fair rotation!\n';
    } else {
      content += 'RECOMMENDATIONS:\n';
      content += '---------------\n';
      content += '• Consider prioritizing staff who haven\'t floated recently\n';
      content += '• Check if any staff need to be placed on hold\n';
      content += '• Review queue order if needed\n';
    }

    return {
      title: 'Missed Floats Report',
      content: content
    };

  } catch (error) {
    console.error('Error generating missed floats report:', error);
    return {
      title: 'Missed Floats Report',
      content: 'Error generating report: ' + error.message
    };
  }
}
