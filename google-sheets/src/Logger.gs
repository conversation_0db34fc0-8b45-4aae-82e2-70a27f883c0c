/**
 * Float Tracker System - Logger Module
 * Google Sheets + Apps Script Implementation
 * 
 * Handles all audit logging and system event tracking.
 * Provides comprehensive logging for security and compliance.
 */

/**
 * Log an action to the Float Log sheet
 * @param {string} staffName - Name of staff member involved
 * @param {string} action - Type of action performed
 * @param {string} unit - Unit involved (if applicable)
 * @param {string} admin - Name of admin performing action (if applicable)
 * @param {string} notes - Additional notes about the action
 */
function logAction(staffName, action, unit = '', admin = '', notes = '') {
  try {
    console.log(`Logging action: ${action} for ${staffName}`);
    
    const logSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.LOG);
    
    // Create timestamp
    const timestamp = new Date();
    
    // Prepare log entry
    const logEntry = [
      timestamp,           // Column A: Timestamp
      staffName,          // Column B: Staff Name
      action,             // Column C: Action
      unit,               // Column D: Unit
      admin,              // Column E: Admin
      notes               // Column F: Notes
    ];
    
    // Add the log entry
    logSheet.appendRow(logEntry);
    
    // Keep log size manageable (optional - remove old entries if too many)
    maintainLogSize(logSheet);
    
    console.log(`Action logged successfully: ${action}`);
    
  } catch (error) {
    console.error('Error logging action:', error);
    // Don't throw error to avoid breaking main functionality
  }
}

/**
 * Maintain log size by removing old entries if log gets too large
 * @param {Sheet} logSheet - The log sheet to maintain
 */
function maintainLogSize(logSheet) {
  try {
    const maxLogEntries = 1000; // Keep last 1000 entries
    const data = logSheet.getDataRange().getValues();
    
    if (data.length > maxLogEntries + 1) { // +1 for header row
      const entriesToRemove = data.length - maxLogEntries - 1;
      
      // Delete old rows (starting from row 2, after header)
      logSheet.deleteRows(2, entriesToRemove);
      
      console.log(`Removed ${entriesToRemove} old log entries to maintain size`);
    }
    
  } catch (error) {
    console.error('Error maintaining log size:', error);
  }
}

/**
 * Get recent log entries for a specific staff member
 * @param {string} staffName - Name of staff member
 * @param {number} limit - Maximum number of entries to return (default: 10)
 * @return {Array} Array of log entries
 */
function getStaffLogEntries(staffName, limit = 10) {
  try {
    console.log(`Getting log entries for: ${staffName}`);
    
    const logSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.LOG);
    const logData = logSheet.getDataRange().getValues();
    
    const staffEntries = [];
    
    // Search for entries for this staff member (newest first)
    for (let i = logData.length - 1; i >= 1; i--) { // Skip header row, go backwards
      if (logData[i][LOG_COLUMNS.STAFF_NAME] === staffName) {
        staffEntries.push({
          timestamp: logData[i][LOG_COLUMNS.TIMESTAMP],
          action: logData[i][LOG_COLUMNS.ACTION],
          unit: logData[i][LOG_COLUMNS.UNIT],
          admin: logData[i][LOG_COLUMNS.ADMIN],
          notes: logData[i][LOG_COLUMNS.NOTES]
        });
        
        if (staffEntries.length >= limit) {
          break;
        }
      }
    }
    
    return staffEntries;
    
  } catch (error) {
    console.error('Error getting staff log entries:', error);
    return [];
  }
}

/**
 * Get recent system log entries
 * @param {number} limit - Maximum number of entries to return (default: 20)
 * @return {Array} Array of log entries
 */
function getRecentLogEntries(limit = 20) {
  try {
    console.log('Getting recent log entries');
    
    const logSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.LOG);
    const logData = logSheet.getDataRange().getValues();
    
    const recentEntries = [];
    
    // Get most recent entries (newest first)
    for (let i = Math.max(1, logData.length - limit); i < logData.length; i++) {
      recentEntries.push({
        timestamp: logData[i][LOG_COLUMNS.TIMESTAMP],
        staffName: logData[i][LOG_COLUMNS.STAFF_NAME],
        action: logData[i][LOG_COLUMNS.ACTION],
        unit: logData[i][LOG_COLUMNS.UNIT],
        admin: logData[i][LOG_COLUMNS.ADMIN],
        notes: logData[i][LOG_COLUMNS.NOTES]
      });
    }
    
    // Reverse to get newest first
    return recentEntries.reverse();
    
  } catch (error) {
    console.error('Error getting recent log entries:', error);
    return [];
  }
}

/**
 * Search log entries by action type
 * @param {string} actionType - Type of action to search for
 * @param {number} limit - Maximum number of entries to return (default: 50)
 * @return {Array} Array of matching log entries
 */
function searchLogByAction(actionType, limit = 50) {
  try {
    console.log(`Searching log for action: ${actionType}`);
    
    const logSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.LOG);
    const logData = logSheet.getDataRange().getValues();
    
    const matchingEntries = [];
    
    // Search for matching actions (newest first)
    for (let i = logData.length - 1; i >= 1; i--) { // Skip header row, go backwards
      if (logData[i][LOG_COLUMNS.ACTION] === actionType) {
        matchingEntries.push({
          timestamp: logData[i][LOG_COLUMNS.TIMESTAMP],
          staffName: logData[i][LOG_COLUMNS.STAFF_NAME],
          action: logData[i][LOG_COLUMNS.ACTION],
          unit: logData[i][LOG_COLUMNS.UNIT],
          admin: logData[i][LOG_COLUMNS.ADMIN],
          notes: logData[i][LOG_COLUMNS.NOTES]
        });
        
        if (matchingEntries.length >= limit) {
          break;
        }
      }
    }
    
    return matchingEntries;
    
  } catch (error) {
    console.error('Error searching log by action:', error);
    return [];
  }
}

/**
 * Get log entries within a date range
 * @param {Date} startDate - Start date for search
 * @param {Date} endDate - End date for search
 * @return {Array} Array of log entries within the date range
 */
function getLogEntriesByDateRange(startDate, endDate) {
  try {
    console.log(`Getting log entries from ${startDate} to ${endDate}`);
    
    const logSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.LOG);
    const logData = logSheet.getDataRange().getValues();
    
    const entriesInRange = [];
    
    for (let i = 1; i < logData.length; i++) { // Skip header row
      const entryDate = new Date(logData[i][LOG_COLUMNS.TIMESTAMP]);
      
      if (entryDate >= startDate && entryDate <= endDate) {
        entriesInRange.push({
          timestamp: logData[i][LOG_COLUMNS.TIMESTAMP],
          staffName: logData[i][LOG_COLUMNS.STAFF_NAME],
          action: logData[i][LOG_COLUMNS.ACTION],
          unit: logData[i][LOG_COLUMNS.UNIT],
          admin: logData[i][LOG_COLUMNS.ADMIN],
          notes: logData[i][LOG_COLUMNS.NOTES]
        });
      }
    }
    
    // Sort by timestamp (newest first)
    entriesInRange.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    
    return entriesInRange;
    
  } catch (error) {
    console.error('Error getting log entries by date range:', error);
    return [];
  }
}

/**
 * Export log data as CSV string
 * @param {number} days - Number of days back to export (default: 30)
 * @return {string} CSV formatted log data
 */
function exportLogAsCSV(days = 30) {
  try {
    console.log(`Exporting log data for last ${days} days`);
    
    const endDate = new Date();
    const startDate = new Date(endDate.getTime() - (days * 24 * 60 * 60 * 1000));
    
    const logEntries = getLogEntriesByDateRange(startDate, endDate);
    
    // Create CSV header
    let csv = 'Timestamp,Staff Name,Action,Unit,Admin,Notes\n';
    
    // Add data rows
    for (const entry of logEntries) {
      const row = [
        entry.timestamp,
        entry.staffName,
        entry.action,
        entry.unit,
        entry.admin,
        entry.notes
      ].map(field => `"${field}"`).join(',');
      
      csv += row + '\n';
    }
    
    return csv;
    
  } catch (error) {
    console.error('Error exporting log as CSV:', error);
    return 'Error exporting log data: ' + error.message;
  }
}

/**
 * Clear old log entries (admin function)
 * @param {number} daysToKeep - Number of days of log data to keep
 * @return {Object} Result object with success status
 */
function clearOldLogEntries(daysToKeep = 90) {
  try {
    console.log(`Clearing log entries older than ${daysToKeep} days`);
    
    const logSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.LOG);
    const logData = logSheet.getDataRange().getValues();
    
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
    
    let rowsToDelete = [];
    
    // Find rows to delete (older than cutoff date)
    for (let i = 1; i < logData.length; i++) { // Skip header row
      const entryDate = new Date(logData[i][LOG_COLUMNS.TIMESTAMP]);
      
      if (entryDate < cutoffDate) {
        rowsToDelete.push(i + 1); // +1 because sheet rows are 1-based
      }
    }
    
    // Delete rows in reverse order to maintain row indices
    rowsToDelete.reverse();
    for (const rowIndex of rowsToDelete) {
      logSheet.deleteRow(rowIndex);
    }
    
    const deletedCount = rowsToDelete.length;
    
    // Log the cleanup action
    logAction('System', 'Log Cleanup', '', 'System', `Deleted ${deletedCount} old log entries`);
    
    console.log(`Deleted ${deletedCount} old log entries`);
    
    return {
      success: true,
      message: `Successfully deleted ${deletedCount} log entries older than ${daysToKeep} days.`
    };
    
  } catch (error) {
    console.error('Error clearing old log entries:', error);
    return {
      success: false,
      message: 'Failed to clear old log entries: ' + error.message
    };
  }
}

/**
 * Get system statistics from log data
 * @return {Object} Statistics object
 */
function getSystemStatistics() {
  try {
    console.log('Generating system statistics');
    
    const logSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.LOG);
    const logData = logSheet.getDataRange().getValues();
    
    const stats = {
      totalLogEntries: logData.length - 1, // Exclude header
      totalFloats: 0,
      totalAdminActions: 0,
      totalUserActions: 0,
      mostActiveUser: '',
      mostActiveUserCount: 0,
      systemStartDate: null,
      lastActivity: null
    };
    
    const userActionCounts = {};
    
    for (let i = 1; i < logData.length; i++) { // Skip header row
      const timestamp = new Date(logData[i][LOG_COLUMNS.TIMESTAMP]);
      const staffName = logData[i][LOG_COLUMNS.STAFF_NAME];
      const action = logData[i][LOG_COLUMNS.ACTION];
      const admin = logData[i][LOG_COLUMNS.ADMIN];
      
      // Track dates
      if (!stats.systemStartDate || timestamp < stats.systemStartDate) {
        stats.systemStartDate = timestamp;
      }
      if (!stats.lastActivity || timestamp > stats.lastActivity) {
        stats.lastActivity = timestamp;
      }
      
      // Count action types
      if (action === 'Float Submitted' || action === 'Admin Override Float') {
        stats.totalFloats++;
      }
      
      if (admin && admin !== '' && admin !== 'System') {
        stats.totalAdminActions++;
      } else {
        stats.totalUserActions++;
      }
      
      // Count user actions
      if (staffName && staffName !== 'System') {
        if (!userActionCounts[staffName]) {
          userActionCounts[staffName] = 0;
        }
        userActionCounts[staffName]++;
      }
    }
    
    // Find most active user
    for (const [userName, count] of Object.entries(userActionCounts)) {
      if (count > stats.mostActiveUserCount) {
        stats.mostActiveUser = userName;
        stats.mostActiveUserCount = count;
      }
    }
    
    return stats;
    
  } catch (error) {
    console.error('Error generating system statistics:', error);
    return null;
  }
}
