/**
 * Float Tracker System - UI Helpers Module
 * Google Sheets + Apps Script Implementation
 * 
 * Provides utility functions for user interface operations,
 * formatting, and visual enhancements.
 */

/**
 * Format the Float Queue sheet with proper styling and protection
 */
function formatFloatQueueSheet() {
  try {
    console.log('Formatting Float Queue sheet');
    
    const queueSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.QUEUE);
    
    // Set column widths for better readability
    queueSheet.setColumnWidth(1, 150); // Staff Name
    queueSheet.setColumnWidth(2, 120); // Last Floated
    queueSheet.setColumnWidth(3, 100); // Float Unit
    queueSheet.setColumnWidth(4, 80);  // Status
    queueSheet.setColumnWidth(5, 120); // Hold Reason
    
    // Format header row
    const headerRange = queueSheet.getRange(1, 1, 1, 5);
    headerRange.setFontWeight('bold');
    headerRange.setBackground('#4285f4');
    headerRange.setFontColor('#ffffff');
    headerRange.setHorizontalAlignment('center');
    
    // Format data rows
    const dataRange = queueSheet.getRange(2, 1, queueSheet.getLastRow() - 1, 5);
    dataRange.setVerticalAlignment('middle');
    
    // Format Last Floated column as date/time
    if (queueSheet.getLastRow() > 1) {
      const lastFloatedRange = queueSheet.getRange(2, 2, queueSheet.getLastRow() - 1, 1);
      lastFloatedRange.setNumberFormat('mm/dd/yyyy hh:mm');
    }
    
    // Add borders
    const allDataRange = queueSheet.getRange(1, 1, queueSheet.getLastRow(), 5);
    allDataRange.setBorder(true, true, true, true, true, true);
    
    console.log('Float Queue sheet formatted successfully');
    
  } catch (error) {
    console.error('Error formatting Float Queue sheet:', error);
  }
}

/**
 * Format the Float Log sheet with proper styling
 */
function formatFloatLogSheet() {
  try {
    console.log('Formatting Float Log sheet');
    
    const logSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.LOG);
    
    // Set column widths
    logSheet.setColumnWidth(1, 130); // Timestamp
    logSheet.setColumnWidth(2, 120); // Staff Name
    logSheet.setColumnWidth(3, 120); // Action
    logSheet.setColumnWidth(4, 100); // Unit
    logSheet.setColumnWidth(5, 100); // Admin
    logSheet.setColumnWidth(6, 200); // Notes
    
    // Format header row
    const headerRange = logSheet.getRange(1, 1, 1, 6);
    headerRange.setFontWeight('bold');
    headerRange.setBackground('#ea4335');
    headerRange.setFontColor('#ffffff');
    headerRange.setHorizontalAlignment('center');
    
    // Format timestamp column
    if (logSheet.getLastRow() > 1) {
      const timestampRange = logSheet.getRange(2, 1, logSheet.getLastRow() - 1, 1);
      timestampRange.setNumberFormat('mm/dd/yyyy hh:mm:ss');
    }
    
    // Add borders
    const allDataRange = logSheet.getRange(1, 1, logSheet.getLastRow(), 6);
    allDataRange.setBorder(true, true, true, true, true, true);
    
    console.log('Float Log sheet formatted successfully');
    
  } catch (error) {
    console.error('Error formatting Float Log sheet:', error);
  }
}

/**
 * Create and format buttons on the Float Queue sheet
 */
function createFloatButtons() {
  try {
    console.log('Creating float buttons');
    
    const queueSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.QUEUE);
    
    // Note: In Google Sheets, buttons are typically created through the Insert > Drawing menu
    // This function provides instructions for manual button creation
    
    const instructions = `
    To create buttons in Google Sheets:
    
    1. FLOAT Button:
       - Go to Insert > Drawing
       - Add a text box with "FLOAT"
       - Style: Blue background (#4285f4), white text, bold
       - Size: approximately 100x40 pixels
       - Save and Close
       - Click the button and assign script: showFloatDialog
       - Position: Cell G2
    
    2. Admin Panel Button:
       - Go to Insert > Drawing
       - Add a text box with "Admin Panel"
       - Style: Red background (#ea4335), white text, bold
       - Size: approximately 120x40 pixels
       - Save and Close
       - Click the button and assign script: showAdminPanel
       - Position: Cell G4
    
    3. Refresh Button (optional):
       - Go to Insert > Drawing
       - Add a text box with "Refresh"
       - Style: Green background (#34a853), white text, bold
       - Size: approximately 100x40 pixels
       - Save and Close
       - Click the button and assign script: refreshSystem
       - Position: Cell G6
    `;
    
    // Add instructions as a comment to cell G1
    queueSheet.getRange('G1').setNote(instructions);
    queueSheet.getRange('G1').setValue('Button Instructions →');
    queueSheet.getRange('G1').setFontStyle('italic');
    queueSheet.getRange('G1').setFontColor('#666666');
    
    console.log('Button creation instructions added');
    
  } catch (error) {
    console.error('Error creating button instructions:', error);
  }
}

/**
 * Apply conditional formatting to highlight queue status
 */
function applyConditionalFormatting() {
  try {
    console.log('Applying conditional formatting');
    
    const queueSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.QUEUE);
    
    // Clear existing conditional formatting
    queueSheet.clearConditionalFormatRules();
    
    const rules = [];
    
    // Rule 1: Highlight "Hold" status in red
    const holdRule = SpreadsheetApp.newConditionalFormatRule()
      .whenTextEqualTo('Hold')
      .setBackground('#ffcccb')
      .setRanges([queueSheet.getRange('D:D')]) // Status column
      .build();
    rules.push(holdRule);
    
    // Rule 2: Highlight "Active" status in light green
    const activeRule = SpreadsheetApp.newConditionalFormatRule()
      .whenTextEqualTo('Active')
      .setBackground('#d4edda')
      .setRanges([queueSheet.getRange('D:D')]) // Status column
      .build();
    rules.push(activeRule);
    
    // Apply all rules
    queueSheet.setConditionalFormatRules(rules);
    
    console.log('Conditional formatting applied');
    
  } catch (error) {
    console.error('Error applying conditional formatting:', error);
  }
}

/**
 * Protect important sheets and ranges
 */
function protectSheets() {
  try {
    console.log('Setting up sheet protection');
    
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    
    // Protect UserData sheet (completely hidden from regular users)
    const userDataSheet = ss.getSheetByName(SHEET_NAMES.USERDATA);
    if (userDataSheet) {
      const protection = userDataSheet.protect();
      protection.setDescription('User authentication data - Admin only');
      protection.setWarningOnly(false);
      
      // Hide the sheet
      userDataSheet.hideSheet();
    }
    
    // Protect Settings sheet
    const settingsSheet = ss.getSheetByName(SHEET_NAMES.SETTINGS);
    if (settingsSheet) {
      const protection = settingsSheet.protect();
      protection.setDescription('System settings - Admin only');
      protection.setWarningOnly(false);
      
      // Hide the sheet
      settingsSheet.hideSheet();
    }
    
    // Protect header rows in main sheets
    const queueSheet = ss.getSheetByName(SHEET_NAMES.QUEUE);
    if (queueSheet) {
      const headerProtection = queueSheet.getRange(1, 1, 1, 5).protect();
      headerProtection.setDescription('Queue headers - Do not edit');
      headerProtection.setWarningOnly(true);
    }
    
    const logSheet = ss.getSheetByName(SHEET_NAMES.LOG);
    if (logSheet) {
      const headerProtection = logSheet.getRange(1, 1, 1, 6).protect();
      headerProtection.setDescription('Log headers - Do not edit');
      headerProtection.setWarningOnly(true);
    }
    
    console.log('Sheet protection configured');
    
  } catch (error) {
    console.error('Error setting up sheet protection:', error);
  }
}

/**
 * Create a dashboard summary in the Float Queue sheet
 */
function createDashboard() {
  try {
    console.log('Creating dashboard summary');
    
    const queueSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.QUEUE);
    
    // Dashboard area starts at column I
    const dashboardStartCol = 9; // Column I
    
    // Clear dashboard area
    queueSheet.getRange(1, dashboardStartCol, 10, 3).clear();
    
    // Dashboard title
    queueSheet.getRange(1, dashboardStartCol).setValue('SYSTEM DASHBOARD');
    queueSheet.getRange(1, dashboardStartCol).setFontWeight('bold');
    queueSheet.getRange(1, dashboardStartCol).setFontSize(14);
    queueSheet.getRange(1, dashboardStartCol).setBackground('#f1f3f4');
    
    // Get queue statistics
    const stats = getQueueStatistics();
    
    if (stats) {
      // Display statistics
      queueSheet.getRange(3, dashboardStartCol).setValue('Total Staff:');
      queueSheet.getRange(3, dashboardStartCol + 1).setValue(stats.totalStaff);
      
      queueSheet.getRange(4, dashboardStartCol).setValue('Active Staff:');
      queueSheet.getRange(4, dashboardStartCol + 1).setValue(stats.activeStaff);
      
      queueSheet.getRange(5, dashboardStartCol).setValue('Staff on Hold:');
      queueSheet.getRange(5, dashboardStartCol + 1).setValue(stats.staffOnHold);
      
      queueSheet.getRange(6, dashboardStartCol).setValue('Never Floated:');
      queueSheet.getRange(6, dashboardStartCol + 1).setValue(stats.neverFloated);
      
      queueSheet.getRange(7, dashboardStartCol).setValue('Next to Float:');
      queueSheet.getRange(7, dashboardStartCol + 1).setValue(stats.nextToFloat || 'None');
      
      if (stats.longestWaitingStaff) {
        queueSheet.getRange(8, dashboardStartCol).setValue('Longest Wait:');
        queueSheet.getRange(8, dashboardStartCol + 1).setValue(`${stats.longestWaitingStaff} (${stats.longestWaitingDays} days)`);
      }
      
      // Format dashboard
      const dashboardRange = queueSheet.getRange(3, dashboardStartCol, 6, 2);
      dashboardRange.setBorder(true, true, true, true, true, true);
      
      // Bold labels
      const labelRange = queueSheet.getRange(3, dashboardStartCol, 6, 1);
      labelRange.setFontWeight('bold');
    }
    
    // Last updated timestamp
    queueSheet.getRange(10, dashboardStartCol).setValue('Last Updated:');
    queueSheet.getRange(10, dashboardStartCol + 1).setValue(new Date());
    queueSheet.getRange(10, dashboardStartCol + 1).setNumberFormat('mm/dd/yyyy hh:mm');
    
    console.log('Dashboard created successfully');
    
  } catch (error) {
    console.error('Error creating dashboard:', error);
  }
}

/**
 * Auto-resize columns to fit content
 */
function autoResizeColumns() {
  try {
    console.log('Auto-resizing columns');
    
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    
    // Resize Float Queue sheet columns
    const queueSheet = ss.getSheetByName(SHEET_NAMES.QUEUE);
    if (queueSheet) {
      queueSheet.autoResizeColumns(1, 5);
    }
    
    // Resize Float Log sheet columns
    const logSheet = ss.getSheetByName(SHEET_NAMES.LOG);
    if (logSheet) {
      logSheet.autoResizeColumns(1, 6);
    }
    
    console.log('Columns auto-resized');
    
  } catch (error) {
    console.error('Error auto-resizing columns:', error);
  }
}

/**
 * Set up the complete UI formatting for the Float Tracker system
 */
function setupCompleteUI() {
  try {
    console.log('Setting up complete UI formatting');
    
    // Format all sheets
    formatFloatQueueSheet();
    formatFloatLogSheet();
    
    // Apply conditional formatting
    applyConditionalFormatting();
    
    // Create dashboard
    createDashboard();
    
    // Set up protection
    protectSheets();
    
    // Create button instructions
    createFloatButtons();
    
    // Auto-resize columns
    autoResizeColumns();
    
    // Update queue colors
    updateQueueColors();
    
    console.log('Complete UI setup finished');
    SpreadsheetApp.getUi().alert('UI formatting complete! Check the Float Queue sheet for button creation instructions.');
    
  } catch (error) {
    console.error('Error setting up complete UI:', error);
    SpreadsheetApp.getUi().alert('Error setting up UI: ' + error.message);
  }
}

/**
 * Refresh the dashboard with current data
 */
function refreshDashboard() {
  try {
    console.log('Refreshing dashboard');
    createDashboard();
  } catch (error) {
    console.error('Error refreshing dashboard:', error);
  }
}

/**
 * Show a custom toast message to the user
 * @param {string} message - Message to display
 * @param {string} title - Title of the toast (optional)
 */
function showToast(message, title = 'Float Tracker') {
  try {
    SpreadsheetApp.getActiveSpreadsheet().toast(message, title, 3);
  } catch (error) {
    console.error('Error showing toast:', error);
  }
}

/**
 * Show a custom alert dialog
 * @param {string} message - Message to display
 * @param {string} title - Title of the dialog (optional)
 */
function showAlert(message, title = 'Float Tracker System') {
  try {
    SpreadsheetApp.getUi().alert(title, message, SpreadsheetApp.getUi().ButtonSet.OK);
  } catch (error) {
    console.error('Error showing alert:', error);
  }
}
