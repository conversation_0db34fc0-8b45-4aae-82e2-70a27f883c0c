/**
 * Float Tracker System - PIN Management Module
 * Google Sheets + Apps Script Implementation
 * 
 * Handles PIN authentication, validation, and security functions.
 * All PINs are stored as hashed values for security.
 */

/**
 * Validate a PIN and return user information if valid
 * @param {string} pin - The 4-digit PIN to validate
 * @return {Object|null} User object if valid, null if invalid
 */
function validatePIN(pin) {
  try {
    console.log('Validating PIN...');
    
    // Basic PIN format validation
    if (!pin || pin.length !== 4 || !/^[0-9]+$/.test(pin)) {
      console.log('Invalid PIN format');
      return null;
    }
    
    // Hash the provided PIN
    const pinHash = hashPIN(pin);
    
    // Get user data
    const userDataSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.USERDATA);
    const userData = userDataSheet.getDataRange().getValues();
    
    // Search for matching PIN hash
    for (let i = 1; i < userData.length; i++) { // Skip header row
      const storedHash = userData[i][1]; // PIN Hash column
      const staffName = userData[i][0]; // Staff Name column
      
      if (storedHash === pinHash) {
        console.log(`PIN validated for user: ${staffName}`);
        
        // Update last login time
        userDataSheet.getRange(i + 1, 4).setValue(new Date()); // Last Login column
        
        return {
          name: staffName,
          lastLogin: new Date(),
          rowIndex: i
        };
      }
    }
    
    console.log('PIN validation failed - no matching hash found');
    return null;
    
  } catch (error) {
    console.error('Error validating PIN:', error);
    return null;
  }
}

/**
 * Generate a secure hash for a PIN
 * Uses MD5 hashing for simplicity (in production, consider stronger hashing)
 * @param {string} pin - The PIN to hash
 * @return {string} The hashed PIN
 */
function hashPIN(pin) {
  try {
    // Add salt to the PIN for additional security
    const salt = 'FloatTracker2024';
    const saltedPIN = pin + salt;
    
    // Generate MD5 hash
    const hash = Utilities.computeDigest(Utilities.DigestAlgorithm.MD5, saltedPIN);
    
    // Convert to hexadecimal string
    const hashString = hash.map(byte => {
      const hex = (byte & 0xFF).toString(16);
      return hex.length === 1 ? '0' + hex : hex;
    }).join('');
    
    return hashString;
    
  } catch (error) {
    console.error('Error hashing PIN:', error);
    throw new Error('Failed to hash PIN');
  }
}

/**
 * Generate a new random 4-digit PIN
 * Ensures the PIN is unique across all users
 * @return {string} A unique 4-digit PIN
 */
function generateUniquePIN() {
  try {
    console.log('Generating unique PIN...');
    
    const userDataSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.USERDATA);
    const userData = userDataSheet.getDataRange().getValues();
    
    // Get all existing PIN hashes
    const existingHashes = [];
    for (let i = 1; i < userData.length; i++) { // Skip header row
      existingHashes.push(userData[i][1]); // PIN Hash column
    }
    
    let attempts = 0;
    const maxAttempts = 100;
    
    while (attempts < maxAttempts) {
      // Generate random 4-digit PIN
      const pin = Math.floor(1000 + Math.random() * 9000).toString();
      const pinHash = hashPIN(pin);
      
      // Check if this hash already exists
      if (!existingHashes.includes(pinHash)) {
        console.log('Unique PIN generated successfully');
        return pin;
      }
      
      attempts++;
    }
    
    throw new Error('Unable to generate unique PIN after ' + maxAttempts + ' attempts');
    
  } catch (error) {
    console.error('Error generating unique PIN:', error);
    throw error;
  }
}

/**
 * Add a new user with a generated PIN
 * @param {string} staffName - The name of the staff member
 * @return {Object} Result object with success status and PIN
 */
function addNewUser(staffName) {
  try {
    console.log(`Adding new user: ${staffName}`);
    
    // Validate staff name
    if (!staffName || staffName.trim().length === 0) {
      return { success: false, message: 'Staff name cannot be empty.' };
    }
    
    staffName = staffName.trim();
    
    // Check if user already exists
    if (userExists(staffName)) {
      return { success: false, message: 'User already exists in the system.' };
    }
    
    // Generate unique PIN
    const pin = generateUniquePIN();
    const pinHash = hashPIN(pin);
    
    // Add to UserData sheet
    const userDataSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.USERDATA);
    userDataSheet.appendRow([
      staffName,
      pinHash,
      new Date(), // Created Date
      '' // Last Login (empty initially)
    ]);
    
    // Add to Queue sheet
    const queueSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.QUEUE);
    queueSheet.appendRow([
      staffName,
      '', // Last Floated (empty initially)
      '', // Float Unit (empty initially)
      'Active', // Status
      '' // Hold Reason (empty initially)
    ]);
    
    // Log the action
    logAction(staffName, 'User Added', '', 'Admin', 'New user created with PIN');
    
    console.log(`User ${staffName} added successfully with PIN: ${pin}`);
    
    return { 
      success: true, 
      message: `User "${staffName}" added successfully.`,
      pin: pin,
      staffName: staffName
    };
    
  } catch (error) {
    console.error('Error adding new user:', error);
    return { success: false, message: 'Failed to add user: ' + error.message };
  }
}

/**
 * Remove a user from the system
 * @param {string} staffName - The name of the staff member to remove
 * @return {Object} Result object with success status
 */
function removeUser(staffName) {
  try {
    console.log(`Removing user: ${staffName}`);
    
    if (!staffName || staffName.trim().length === 0) {
      return { success: false, message: 'Staff name cannot be empty.' };
    }
    
    staffName = staffName.trim();
    
    // Check if user exists
    if (!userExists(staffName)) {
      return { success: false, message: 'User not found in the system.' };
    }
    
    // Remove from UserData sheet
    const userDataSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.USERDATA);
    const userData = userDataSheet.getDataRange().getValues();
    
    for (let i = 1; i < userData.length; i++) { // Skip header row
      if (userData[i][0] === staffName) { // Staff Name column
        userDataSheet.deleteRow(i + 1);
        break;
      }
    }
    
    // Remove from Queue sheet
    const queueSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.QUEUE);
    const queueData = queueSheet.getDataRange().getValues();
    
    for (let i = 1; i < queueData.length; i++) { // Skip header row
      if (queueData[i][QUEUE_COLUMNS.STAFF_NAME] === staffName) {
        queueSheet.deleteRow(i + 1);
        break;
      }
    }
    
    // Log the action
    logAction(staffName, 'User Removed', '', 'Admin', 'User removed from system');
    
    console.log(`User ${staffName} removed successfully`);
    
    return { 
      success: true, 
      message: `User "${staffName}" removed successfully.` 
    };
    
  } catch (error) {
    console.error('Error removing user:', error);
    return { success: false, message: 'Failed to remove user: ' + error.message };
  }
}

/**
 * Reset a user's PIN to a new random PIN
 * @param {string} staffName - The name of the staff member
 * @return {Object} Result object with success status and new PIN
 */
function resetUserPIN(staffName) {
  try {
    console.log(`Resetting PIN for user: ${staffName}`);
    
    if (!staffName || staffName.trim().length === 0) {
      return { success: false, message: 'Staff name cannot be empty.' };
    }
    
    staffName = staffName.trim();
    
    // Check if user exists
    if (!userExists(staffName)) {
      return { success: false, message: 'User not found in the system.' };
    }
    
    // Generate new PIN
    const newPIN = generateUniquePIN();
    const newPINHash = hashPIN(newPIN);
    
    // Update PIN in UserData sheet
    const userDataSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.USERDATA);
    const userData = userDataSheet.getDataRange().getValues();
    
    for (let i = 1; i < userData.length; i++) { // Skip header row
      if (userData[i][0] === staffName) { // Staff Name column
        userDataSheet.getRange(i + 1, 2).setValue(newPINHash); // PIN Hash column
        userDataSheet.getRange(i + 1, 4).setValue(''); // Clear Last Login
        break;
      }
    }
    
    // Log the action
    logAction(staffName, 'PIN Reset', '', 'Admin', 'PIN reset by administrator');
    
    console.log(`PIN reset successfully for ${staffName}`);
    
    return { 
      success: true, 
      message: `PIN reset successfully for "${staffName}".`,
      pin: newPIN,
      staffName: staffName
    };
    
  } catch (error) {
    console.error('Error resetting PIN:', error);
    return { success: false, message: 'Failed to reset PIN: ' + error.message };
  }
}

/**
 * Check if a user exists in the system
 * @param {string} staffName - The name of the staff member
 * @return {boolean} True if user exists, false otherwise
 */
function userExists(staffName) {
  try {
    const userDataSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.USERDATA);
    const userData = userDataSheet.getDataRange().getValues();
    
    for (let i = 1; i < userData.length; i++) { // Skip header row
      if (userData[i][0] === staffName) { // Staff Name column
        return true;
      }
    }
    
    return false;
    
  } catch (error) {
    console.error('Error checking if user exists:', error);
    return false;
  }
}

/**
 * Check if a user is currently on hold
 * @param {string} staffName - The name of the staff member
 * @return {boolean} True if user is on hold, false otherwise
 */
function isUserOnHold(staffName) {
  try {
    const queueSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.QUEUE);
    const queueData = queueSheet.getDataRange().getValues();
    
    for (let i = 1; i < queueData.length; i++) { // Skip header row
      if (queueData[i][QUEUE_COLUMNS.STAFF_NAME] === staffName) {
        const status = queueData[i][QUEUE_COLUMNS.STATUS];
        return status !== 'Active';
      }
    }
    
    return false; // User not found, assume not on hold
    
  } catch (error) {
    console.error('Error checking user hold status:', error);
    return false;
  }
}

/**
 * Get all users in the system
 * @return {Array} Array of user objects
 */
function getAllUsers() {
  try {
    const userDataSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.USERDATA);
    const userData = userDataSheet.getDataRange().getValues();
    
    const users = [];
    for (let i = 1; i < userData.length; i++) { // Skip header row
      users.push({
        name: userData[i][0],
        createdDate: userData[i][2],
        lastLogin: userData[i][3]
      });
    }
    
    return users;
    
  } catch (error) {
    console.error('Error getting all users:', error);
    return [];
  }
}

/**
 * Validate admin password
 * @param {string} password - The admin password to validate
 * @return {boolean} True if password is correct, false otherwise
 */
function validateAdminPassword(password) {
  try {
    const settingsSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.SETTINGS);
    const settingsData = settingsSheet.getDataRange().getValues();
    
    // Find admin password setting
    for (let i = 0; i < settingsData.length; i++) {
      if (settingsData[i][0] === 'ADMIN_PASSWORD') {
        const storedPassword = settingsData[i][1];
        return password === storedPassword;
      }
    }
    
    // Fallback to default password if not found in settings
    return password === ADMIN_PASSWORD_DEFAULT;
    
  } catch (error) {
    console.error('Error validating admin password:', error);
    return false;
  }
}
