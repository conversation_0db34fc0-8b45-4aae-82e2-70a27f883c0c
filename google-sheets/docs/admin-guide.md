# Float Tracker System - Administrator Guide

Complete administrative guide for managing the Google Sheets Float Tracker System.

## 🔐 Admin Access

### Default Credentials
- **Admin Password**: `FloatAdmin2024`
- **⚠️ IMPORTANT**: Change this password immediately after installation

### Accessing Admin Panel
1. **Click** the red "Admin Panel" button on the Float Queue sheet
2. **Enter** the admin password
3. **Enter** your name (for audit logging)
4. **Click** "Access Admin Panel"

## 👥 User Management

### Adding New Users

#### Process:
1. **Access** Admin Panel → "Add New User"
2. **Enter** the staff member's full name exactly as it should appear
3. **Click** "Add User"
4. **Write down** the generated PIN immediately
5. **Give** the PIN to the staff member privately

#### Important Notes:
- **PINs are only shown once** - write them down immediately
- **Names must be exact** - they're used for identification
- **Each user gets a unique 4-digit PIN** automatically
- **Users are automatically added to the queue** as "Active"

### Removing Users

#### Process:
1. **Access** Admin Panel → "Remove User"
2. **Enter** the exact staff member name
3. **Confirm** the removal when prompted
4. **User is removed** from both queue and system

#### Important Notes:
- **Removal is permanent** - cannot be undone
- **All user data is deleted** including PIN and history
- **Use carefully** - consider holding staff instead of removing

### Resetting User PINs

#### When to Reset:
- Staff member forgot their PIN
- PIN may have been compromised
- Staff member requests a new PIN
- Security incident involving the user

#### Process:
1. **Access** Admin Panel → "Reset User PIN"
2. **Enter** the exact staff member name
3. **Write down** the new PIN immediately
4. **Give** the new PIN to the staff member privately

## 🎛️ Queue Management

### Holding Staff Members

#### Common Reasons:
- **Vacation/PTO**: Staff member is off work
- **Training**: Staff member in orientation or training
- **Unavailable**: Temporary unavailability
- **Medical**: Medical leave or restrictions
- **Administrative**: Administrative duties

#### Process:
1. **Access** Admin Panel → "Hold Staff"
2. **Enter** staff member name
3. **Enter** reason for hold (be specific)
4. **Staff status** changes to "Hold"
5. **Staff cannot float** while on hold

### Releasing Staff from Hold

#### Process:
1. **Access** Admin Panel → "Release Staff from Hold"
2. **Enter** staff member name
3. **Staff status** changes back to "Active"
4. **Staff can float** normally again

### Override Floats

#### When to Use:
- **Emergency staffing** needs
- **Staff member unavailable** to enter PIN
- **System issues** preventing normal float
- **Administrative** float assignments

#### Process:
1. **Access** Admin Panel → "Override Float"
2. **Enter** staff member name
3. **Select** destination unit
4. **Float is processed** without PIN requirement
5. **Staff moves** to bottom of queue normally

### Reset Queue Order

#### When to Use:
- **Starting fresh** with random order
- **Fairness concerns** about current order
- **System issues** with queue integrity
- **Administrative decision** to randomize

#### Process:
1. **Access** Admin Panel → "Reset Queue Order"
2. **Confirm** the action (cannot be undone)
3. **All active staff** are randomly reordered
4. **Staff on hold** maintain their positions

## 📊 Reports and Analytics

### Float Summary Report

#### Information Provided:
- **Float counts** by staff member
- **Recent floats** (last 10)
- **Total system activity**
- **Usage patterns**

#### How to Access:
1. **Access** Admin Panel → "View Float Summary"
2. **Review** the generated report
3. **Use** for staffing decisions and fairness monitoring

### Missed Floats Report

#### Information Provided:
- **Staff who never floated**
- **Staff not floated in 24 hours**
- **Staff not floated in 7 days**
- **Recommendations** for action

#### How to Access:
1. **Access** Admin Panel → "View Missed Floats"
2. **Review** staff who may need priority
3. **Take action** to ensure fair rotation

## 🔧 System Maintenance

### Regular Maintenance Tasks

#### Daily:
- **Check** missed floats report
- **Verify** queue is functioning properly
- **Address** any user issues or questions

#### Weekly:
- **Review** float summary report
- **Check** for staff needing PIN resets
- **Verify** hold statuses are current

#### Monthly:
- **Review** system logs for issues
- **Update** staff list as needed
- **Check** admin password security

### Troubleshooting Common Issues

#### Queue Not Updating
**Symptoms**: Colors not changing, positions not updating
**Solutions**:
1. **Refresh** the page
2. **Check** Apps Script execution logs
3. **Run** manual refresh function
4. **Verify** triggers are working

#### Buttons Not Working
**Symptoms**: FLOAT or Admin buttons unresponsive
**Solutions**:
1. **Check** script assignments on buttons
2. **Verify** Apps Script permissions
3. **Re-authorize** scripts if needed
4. **Recreate** buttons if necessary

#### PIN Validation Failing
**Symptoms**: Valid PINs being rejected
**Solutions**:
1. **Check** UserData sheet for corruption
2. **Verify** PIN hashing is working
3. **Reset** affected user PINs
4. **Check** Apps Script logs for errors

#### Data Corruption
**Symptoms**: Missing users, incorrect data
**Solutions**:
1. **Use** queue validation function
2. **Check** Google Sheets version history
3. **Restore** from backup if needed
4. **Manually** correct data if minor

## 🔒 Security Management

### Password Management

#### Changing Admin Password:
1. **Go to** Settings sheet (unhide if needed)
2. **Find** ADMIN_PASSWORD row
3. **Change** the value in column B
4. **Hide** the sheet again
5. **Test** new password works

#### Password Requirements:
- **Minimum 8 characters**
- **Include** letters and numbers
- **Avoid** common words
- **Change** regularly (quarterly recommended)

### Access Control

#### Sheet Permissions:
- **UserData sheet**: Hidden and protected
- **Settings sheet**: Hidden and protected
- **Float Queue**: Headers protected, data editable
- **Float Log**: Headers protected, data append-only

#### User Permissions:
- **Regular users**: Editor access to main sheets
- **Admins**: Full access to all sheets
- **View-only users**: Commenter access

### Audit Logging

#### What's Logged:
- **All float submissions**
- **Admin actions**
- **User additions/removals**
- **PIN resets**
- **System events**

#### Reviewing Logs:
1. **Open** Float Log sheet
2. **Filter** by date, user, or action
3. **Export** data if needed for reporting
4. **Monitor** for suspicious activity

## 📋 Best Practices

### User Management
- **Verify** staff names are spelled correctly
- **Keep** PIN distribution secure and private
- **Document** reasons for holds clearly
- **Remove** users promptly when they leave

### Queue Management
- **Monitor** fairness regularly
- **Address** missed float issues quickly
- **Use** override sparingly and document reasons
- **Communicate** changes to staff

### System Administration
- **Back up** data regularly (Google handles this automatically)
- **Test** system functionality weekly
- **Keep** documentation updated
- **Train** backup administrators

## 🆘 Emergency Procedures

### System Down
1. **Verify** internet connectivity
2. **Check** Google Sheets service status
3. **Use** manual backup procedures
4. **Document** manual floats for later entry
5. **Contact** IT support if needed

### Data Loss
1. **Check** Google Sheets version history
2. **Restore** from most recent good version
3. **Manually** recreate recent changes
4. **Verify** all users and data are correct
5. **Test** system functionality

### Security Breach
1. **Change** admin password immediately
2. **Reset** all user PINs
3. **Review** audit logs for suspicious activity
4. **Report** incident per facility policy
5. **Document** actions taken

## 📞 Support Contacts

### Google Workspace Support
- **Admin Console**: admin.google.com
- **Support**: support.google.com
- **Status**: status.google.com

### Apps Script Support
- **Documentation**: developers.google.com/apps-script
- **Community**: stackoverflow.com (tag: google-apps-script)

## 🔄 System Updates

### Updating Apps Script Code
1. **Go to** Extensions → Apps Script
2. **Make** necessary changes to code files
3. **Save** all changes
4. **Test** functionality thoroughly
5. **Document** changes made

### Adding New Features
1. **Plan** the feature carefully
2. **Test** in a copy of the sheet first
3. **Update** documentation
4. **Train** users on new functionality
5. **Monitor** for issues after deployment

---

## 📝 Quick Reference

### Daily Admin Tasks:
- Check missed floats report
- Address user issues
- Verify queue functioning

### Emergency Contacts:
- IT Support: [Your IT Number]
- Facility Admin: [Admin Number]
- Google Support: [G-Workspace Support]

### Key Functions:
- Add User: Admin Panel → Add New User
- Reset PIN: Admin Panel → Reset User PIN
- Hold Staff: Admin Panel → Hold Staff
- Override Float: Admin Panel → Override Float

---

**For technical issues beyond this guide, contact your IT department or Google Workspace administrator.**
