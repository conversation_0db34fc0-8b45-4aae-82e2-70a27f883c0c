# Float Tracker System - User Guide

A comprehensive guide for staff members using the Google Sheets Float Tracker System.

## 🎯 Overview

The Float Tracker System ensures fair rotation of floating assignments through an automated queue system. Every staff member gets equal opportunities to float, and the system tracks everything automatically.

## 🔐 Getting Started

### Your PIN
- Each staff member has a unique **4-digit PIN**
- Your PIN is provided by your administrator
- **Keep your PIN confidential** - never share it with others
- If you forget your PIN, contact an administrator for a reset

### Accessing the System
1. **Open** the Float Tracker Google Sheet (link provided by admin)
2. **Bookmark** the sheet for easy access
3. **Mobile Access**: Download Google Sheets app for mobile use

## 🚀 How to Submit a Float

### Step 1: Click the FLOAT Button
- **Locate** the blue "FLOAT" button on the Float Queue sheet
- **Click** the button to open the float request dialog

### Step 2: Enter Your PIN
- **Type** your 4-digit PIN in the password field
- **Note**: PIN entry is hidden for security
- **Verify** you entered the correct PIN before proceeding

### Step 3: Select Destination Unit
- **Choose** your destination unit from the dropdown menu:
  - ICU (Intensive Care Unit)
  - ER (Emergency Room)
  - Med/Surg (Medical/Surgical)
  - Pediatrics
  - Maternity
  - OR (Operating Room)
  - Recovery
  - Oncology
  - Cardiology
  - Other

### Step 4: Submit Your Float
- **Click** "Submit Float" to process your request
- **Wait** for confirmation message
- **Check** that your position in the queue has updated

## 📊 Understanding the Queue

### Queue Display
The Float Queue sheet shows:
- **Staff Name**: Your name and colleagues
- **Last Floated**: When you last floated (date/time)
- **Float Unit**: Where you last floated
- **Status**: Your current status (Active/Hold)
- **Hold Reason**: Why you're on hold (if applicable)

### Color Coding
- **Orange Highlight**: Next person to float
- **Green Background**: Active status
- **Red Background**: On hold status

### Your Position
- **Queue Order**: Staff are listed in order of next to float
- **After Floating**: You automatically move to the bottom of the queue
- **Fair Rotation**: Everyone gets equal opportunities

## ✅ What Happens When You Float

### Immediate Effects
1. **Queue Update**: You move to the bottom of the queue
2. **Timestamp**: System records when you floated
3. **Unit Tracking**: System records where you floated
4. **Automatic Save**: All changes saved to Google Drive

### Next Float Opportunity
- **Queue Position**: You'll gradually move up as others float
- **Fair System**: No one can float twice before everyone floats once
- **Automatic Rotation**: System ensures equal opportunities

## 🔍 Checking Your Status

### Current Position
- **Look** at the Float Queue sheet
- **Find** your name in the list
- **Higher** on the list = sooner to float

### Float History
- **Last Floated** column shows your most recent float
- **Float Unit** column shows where you went
- **Admin** can provide detailed history if needed

### Hold Status
- **Status** column shows if you're "Active" or "Hold"
- **Hold Reason** explains why you're on hold
- **Contact** admin to be released from hold

## 📱 Mobile Usage

### Google Sheets App
1. **Download** Google Sheets app
2. **Sign in** with your Google account
3. **Open** the Float Tracker sheet
4. **Use** buttons normally (may need to tap twice)

### Mobile Browser
1. **Open** any web browser
2. **Go to** sheets.google.com
3. **Open** the Float Tracker sheet
4. **Use** normally (buttons work in browser)

### Mobile Tips
- **Zoom in** if buttons are small
- **Rotate** to landscape for better view
- **Refresh** page if updates don't appear

## ⚠️ Common Issues & Solutions

### "Invalid PIN" Error
**Problem**: System doesn't accept your PIN
**Solutions**:
- Double-check you entered the correct 4-digit PIN
- Make sure you're using numbers only (no letters)
- Contact admin if you've forgotten your PIN
- Ask admin to reset your PIN if needed

### "You are on hold" Message
**Problem**: System won't let you float
**Solutions**:
- Check the Hold Reason in your queue entry
- Contact administrator to be released from hold
- Common hold reasons: vacation, training, unavailable

### Button Doesn't Work
**Problem**: FLOAT button doesn't respond
**Solutions**:
- Try clicking the button again
- Refresh the page (Ctrl+F5 or Cmd+R)
- Try using a different browser
- Contact admin if problem persists

### Queue Not Updating
**Problem**: Your position doesn't change after floating
**Solutions**:
- Wait 30 seconds and refresh the page
- Check if your float was actually processed
- Look for confirmation message
- Contact admin if queue seems stuck

### Can't Access Sheet
**Problem**: Can't open the Float Tracker sheet
**Solutions**:
- Check your internet connection
- Verify you're signed into the correct Google account
- Ask admin to check your sharing permissions
- Try opening in an incognito/private browser window

## 📋 Best Practices

### PIN Security
- **Never share** your PIN with anyone
- **Don't write** your PIN where others can see it
- **Change** your PIN if you suspect it's compromised
- **Report** any suspicious activity to admin

### Fair Usage
- **Only float** when you're actually floating
- **Don't** submit false float requests
- **Respect** the queue order
- **Communicate** with admin about schedule changes

### System Maintenance
- **Keep** the sheet open during your shift for real-time updates
- **Refresh** occasionally to see latest changes
- **Report** any system issues to admin immediately
- **Don't** edit the sheet directly (use buttons only)

## 🆘 Getting Help

### For Technical Issues
1. **Try** the solutions in "Common Issues" section
2. **Refresh** the page and try again
3. **Contact** your administrator
4. **Provide** specific error messages if any

### For Administrative Issues
1. **Contact** your charge nurse or supervisor
2. **Explain** the situation clearly
3. **Provide** your name and the issue details
4. **Wait** for admin to resolve the issue

### Emergency Situations
- **If** the system is completely down during critical staffing needs
- **Contact** your supervisor immediately
- **Use** backup manual procedures as directed
- **Document** any manual floats for later entry

## 📞 Contact Information

### System Administrator
- **Name**: [Your Admin Name]
- **Email**: [<EMAIL>]
- **Phone**: [Admin Phone Number]
- **Hours**: [Admin Available Hours]

### Technical Support
- **IT Help Desk**: [IT Phone Number]
- **Email**: [<EMAIL>]
- **Hours**: [IT Support Hours]

## 🔄 System Updates

### Automatic Updates
- **Google Sheets** automatically saves all changes
- **No manual saving** required
- **Version history** available through Google Sheets
- **Real-time** collaboration with other users

### Scheduled Maintenance
- **System** may be briefly unavailable during updates
- **Advance notice** will be provided for planned maintenance
- **Backup procedures** will be communicated if needed

---

## 📝 Quick Reference

### To Float:
1. Click FLOAT button
2. Enter your 4-digit PIN
3. Select destination unit
4. Click Submit Float

### If You Have Issues:
1. Check Common Issues section
2. Try refreshing the page
3. Contact your administrator

### Remember:
- Keep your PIN confidential
- Only submit real float requests
- Check your queue position regularly
- Report issues promptly

---

**Need more help? Contact your administrator or refer to the Admin Guide for additional information.**
