# Float Tracker System - Apps Script Technical Guide

Technical documentation for developers and system administrators working with the Apps Script implementation.

## 🏗️ Architecture Overview

### System Components
- **Google Sheets**: Data storage and user interface
- **Apps Script**: Server-side automation and business logic
- **HTML Service**: Custom dialogs and user interfaces
- **Triggers**: Event-driven automation

### Code Structure
```
src/
├── Code.gs           # Main system logic and initialization
├── PINManager.gs     # PIN authentication and user management
├── AdminPanel.gs     # Administrative functions and UI
├── QueueManager.gs   # Queue operations and management
├── Logger.gs         # Audit logging and reporting
└── UIHelpers.gs      # User interface utilities and formatting
```

## 📊 Data Model

### Sheet Structure

#### Float Queue Sheet
| Column | Name | Type | Description |
|--------|------|------|-------------|
| A | Staff Name | String | Full name of staff member |
| B | Last Floated | Date | Timestamp of last float |
| C | Float Unit | String | Destination unit of last float |
| D | Status | String | Active/Hold status |
| E | Hold Reason | String | Reason for hold (if applicable) |

#### Float Log Sheet
| Column | Name | Type | Description |
|--------|------|------|-------------|
| A | Timestamp | Date | When action occurred |
| B | Staff Name | String | Staff member involved |
| C | Action | String | Type of action performed |
| D | Unit | String | Unit involved (if applicable) |
| E | Admin | String | Admin who performed action |
| F | Notes | String | Additional details |

#### UserData Sheet (Hidden)
| Column | Name | Type | Description |
|--------|------|------|-------------|
| A | Staff Name | String | Full name of staff member |
| B | PIN Hash | String | MD5 hash of PIN + salt |
| C | Created Date | Date | When user was created |
| D | Last Login | Date | Last successful PIN validation |

#### Settings Sheet (Hidden)
| Column | Name | Type | Description |
|--------|------|------|-------------|
| A | Setting | String | Setting name |
| B | Value | String | Setting value |

## 🔧 Core Functions

### Main System Functions

#### `initializeFloatTracker()`
- **Purpose**: Initialize the system on first run
- **Called**: Manually or on spreadsheet open
- **Actions**: Verifies sheets, sets up data, configures triggers

#### `showFloatDialog()`
- **Purpose**: Display float request dialog
- **Triggered**: FLOAT button click
- **Returns**: HTML dialog for PIN entry and unit selection

#### `processFloat(pin, unit)`
- **Purpose**: Process a float request
- **Parameters**: 
  - `pin` (string): User's 4-digit PIN
  - `unit` (string): Destination unit
- **Returns**: Result object with success status

#### `onEdit(e)`
- **Purpose**: Handle spreadsheet edit events
- **Triggered**: Any cell edit in the spreadsheet
- **Actions**: Updates queue colors when Float Queue is modified

### PIN Management Functions

#### `validatePIN(pin)`
- **Purpose**: Validate user PIN and return user info
- **Security**: Uses MD5 hashing with salt
- **Returns**: User object or null

#### `hashPIN(pin)`
- **Purpose**: Generate secure hash for PIN storage
- **Algorithm**: MD5 with salt "FloatTracker2024"
- **Returns**: Hexadecimal hash string

#### `generateUniquePIN()`
- **Purpose**: Generate unique 4-digit PIN
- **Logic**: Random generation with uniqueness check
- **Returns**: 4-digit numeric string

### Queue Management Functions

#### `reorderQueue(staffName)`
- **Purpose**: Move staff member to bottom of queue
- **Called**: After successful float
- **Actions**: Maintains fair rotation order

#### `updateQueueColors()`
- **Purpose**: Apply visual highlighting to queue
- **Logic**: Orange highlight for next to float
- **Triggered**: After queue changes

## 🔐 Security Implementation

### PIN Security
```javascript
// PIN hashing with salt
function hashPIN(pin) {
  const salt = 'FloatTracker2024';
  const saltedPIN = pin + salt;
  const hash = Utilities.computeDigest(Utilities.DigestAlgorithm.MD5, saltedPIN);
  return hash.map(byte => {
    const hex = (byte & 0xFF).toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  }).join('');
}
```

### Admin Authentication
```javascript
// Admin password validation
function validateAdminPassword(password) {
  const settingsSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.SETTINGS);
  const settingsData = settingsSheet.getDataRange().getValues();
  
  for (let i = 0; i < settingsData.length; i++) {
    if (settingsData[i][0] === 'ADMIN_PASSWORD') {
      return password === settingsData[i][1];
    }
  }
  return false;
}
```

### Sheet Protection
- **UserData Sheet**: Hidden and protected from editing
- **Settings Sheet**: Hidden and protected from editing
- **Header Rows**: Protected with warning-only mode
- **Sensitive Ranges**: Protected from accidental modification

## 🎯 Triggers and Events

### Installed Triggers

#### onEdit Trigger
- **Function**: `onEdit(e)`
- **Event**: Spreadsheet edit
- **Purpose**: Auto-update queue colors
- **Setup**: Manual installation required

### Event Handling
```javascript
function onEdit(e) {
  try {
    const sheet = e.source.getActiveSheet();
    
    if (sheet.getName() === SHEET_NAMES.QUEUE) {
      Utilities.sleep(100); // Allow edit to complete
      updateQueueColors();
    }
  } catch (error) {
    console.error('Error in onEdit handler:', error);
  }
}
```

## 🔍 Error Handling

### Error Patterns
```javascript
function exampleFunction() {
  try {
    // Main logic here
    console.log('Operation started');
    
    // Actual work
    const result = performOperation();
    
    console.log('Operation completed successfully');
    return { success: true, data: result };
    
  } catch (error) {
    console.error('Error in exampleFunction:', error);
    return { success: false, message: error.message };
  }
}
```

### Logging Strategy
- **Console Logging**: For debugging and development
- **Audit Logging**: For user actions and system events
- **Error Logging**: For troubleshooting and monitoring

## 🧪 Testing and Debugging

### Manual Testing Functions
```javascript
// Test PIN validation
function testPINValidation() {
  const testPIN = '1234';
  const user = validatePIN(testPIN);
  console.log('PIN validation result:', user);
}

// Test queue operations
function testQueueOperations() {
  const queue = getCurrentQueue();
  console.log('Current queue:', queue);
  
  const nextToFloat = getNextToFloat();
  console.log('Next to float:', nextToFloat);
}
```

### Debugging Tips
1. **Use console.log()** extensively for debugging
2. **Check Executions tab** in Apps Script editor for runtime logs
3. **Test functions individually** before integration
4. **Use try-catch blocks** to handle errors gracefully
5. **Validate data types** and null checks

### Common Issues and Solutions

#### Script Authorization
**Problem**: "Authorization required" errors
**Solution**: 
```javascript
// Ensure proper authorization in script editor
// Go to Apps Script → Run any function → Authorize
```

#### Trigger Issues
**Problem**: onEdit trigger not working
**Solution**:
```javascript
// Manually install trigger
function installTriggers() {
  ScriptApp.newTrigger('onEdit')
    .onEdit()
    .create();
}
```

## 🚀 Deployment Guide

### Initial Setup
1. **Create** new Google Sheet
2. **Set up** sheet structure (4 sheets with proper headers)
3. **Import** all Apps Script files
4. **Configure** triggers
5. **Test** all functionality
6. **Set up** sheet protection

### Code Deployment
```javascript
// Deployment checklist function
function deploymentChecklist() {
  console.log('=== DEPLOYMENT CHECKLIST ===');
  
  // Verify sheets exist
  const requiredSheets = Object.values(SHEET_NAMES);
  for (const sheetName of requiredSheets) {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(sheetName);
    console.log(`Sheet "${sheetName}": ${sheet ? 'EXISTS' : 'MISSING'}`);
  }
  
  // Test core functions
  console.log('Testing core functions...');
  try {
    updateQueueColors();
    console.log('updateQueueColors: OK');
  } catch (e) {
    console.log('updateQueueColors: ERROR -', e.message);
  }
  
  console.log('=== CHECKLIST COMPLETE ===');
}
```

### Version Control
- **Use** Google Apps Script's built-in version history
- **Document** changes in code comments
- **Test** thoroughly before deployment
- **Keep** backup copies of working versions

## 📈 Performance Optimization

### Best Practices
```javascript
// Batch operations for better performance
function batchUpdateExample() {
  const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAMES.QUEUE);
  
  // Get all data at once
  const data = sheet.getDataRange().getValues();
  
  // Process data in memory
  for (let i = 1; i < data.length; i++) {
    // Modify data array
    data[i][QUEUE_COLUMNS.STATUS] = 'Active';
  }
  
  // Write all data back at once
  sheet.getRange(1, 1, data.length, data[0].length).setValues(data);
}
```

### Optimization Tips
1. **Minimize** sheet read/write operations
2. **Batch** multiple operations together
3. **Use** getDataRange() instead of individual cell reads
4. **Cache** frequently accessed data
5. **Avoid** unnecessary function calls in loops

## 🔧 Customization Guide

### Adding New Units
```javascript
// Update unit dropdown in showFloatDialog()
const unitOptions = [
  'ICU', 'ER', 'Med/Surg', 'Pediatrics', 'Maternity',
  'OR', 'Recovery', 'Oncology', 'Cardiology', 'Other',
  'NEW_UNIT_NAME' // Add new unit here
];
```

### Custom Validation Rules
```javascript
// Add custom validation in processFloat()
function customValidation(staffName, unit) {
  // Example: Restrict certain staff from certain units
  if (staffName === 'John Doe' && unit === 'ICU') {
    return { valid: false, message: 'John Doe cannot float to ICU' };
  }
  
  return { valid: true };
}
```

### Additional Logging
```javascript
// Add custom log actions
function logCustomAction(staffName, details) {
  logAction(staffName, 'Custom Action', '', 'System', details);
}
```

## 📚 API Reference

### Global Constants
- `SHEET_NAMES`: Object containing sheet name constants
- `QUEUE_COLUMNS`: Object containing column index constants
- `LOG_COLUMNS`: Object containing log column index constants
- `ADMIN_PASSWORD_DEFAULT`: Default admin password
- `VERSION`: System version number

### Return Object Patterns
```javascript
// Standard success response
{
  success: true,
  message: "Operation completed successfully",
  data: { /* optional data */ }
}

// Standard error response
{
  success: false,
  message: "Error description",
  error: { /* optional error details */ }
}
```

---

## 🔗 External Resources

- **Apps Script Documentation**: https://developers.google.com/apps-script
- **Google Sheets API**: https://developers.google.com/sheets/api
- **HTML Service Guide**: https://developers.google.com/apps-script/guides/html
- **Triggers Documentation**: https://developers.google.com/apps-script/guides/triggers

---

**This technical guide provides the foundation for understanding, maintaining, and extending the Float Tracker System's Apps Script implementation.**
