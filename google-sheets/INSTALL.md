# Float Tracker System - Google Sheets Installation Guide

Complete step-by-step instructions to set up the Float Tracker System in Google Sheets with Apps Script automation.

## 📋 Prerequisites

### Required
- **Google Account**: Personal Gmail or G-Workspace account
- **Google Sheets Access**: Ability to create and edit spreadsheets
- **Internet Connection**: Required for all functionality

### Recommended
- **G-Workspace Account**: For advanced sharing and admin features
- **Apps Script Permissions**: Ability to run custom scripts (may require admin approval in some organizations)

## 🚀 Installation Method 1: Template Import (Recommended)

### Step 1: Download Template
1. **Download** `Float-Tracker-Template.xlsx` from this folder
2. **Save** to your computer

### Step 2: Import to Google Sheets
1. **Go to** [sheets.google.com](https://sheets.google.com)
2. **Click** "Blank" to create new spreadsheet
3. **File** → **Import**
4. **Upload** → **Select a file from your device**
5. **Choose** `Float-Tracker-Template.xlsx`
6. **Import location**: "Replace spreadsheet"
7. **Click** "Import data"

### Step 3: Rename and Save
1. **Click** "Untitled spreadsheet" at the top
2. **Rename** to "Float Tracker System"
3. **File** → **Move** → Choose destination folder (optional)

### Step 4: Set Up Apps Script
1. **Extensions** → **Apps Script**
2. **Copy and paste** each script file from `/src/` folder:
   - Start with `Code.gs` (replace default code)
   - Add new files for each remaining script
3. **Save** the project (`Ctrl + S`)
4. **Name** the project "Float Tracker System"

### Step 5: Configure Triggers
1. In Apps Script editor, click **Triggers** (clock icon)
2. **Add Trigger**:
   - Function: `onEdit`
   - Event source: "From spreadsheet"
   - Event type: "On edit"
   - Click **Save**
3. **Authorize** the script when prompted

### Step 6: Test Installation
1. **Return to Google Sheets**
2. **Click** the "FLOAT" button
3. **Enter PIN**: 1234 (sample user)
4. **Select** any unit
5. **Verify** the system works

## 🔧 Installation Method 2: Manual Setup

### Step 1: Create New Google Sheet
1. **Go to** [sheets.google.com](https://sheets.google.com)
2. **Click** "Blank" to create new spreadsheet
3. **Rename** to "Float Tracker System"

### Step 2: Create Sheet Structure
Create these sheets (tabs) in order:

#### Sheet 1: "Float Queue"
1. **Rename** "Sheet1" to "Float Queue"
2. **Add headers** in row 1:
   - A1: "Staff Name"
   - B1: "Last Floated"
   - C1: "Float Unit"
   - D1: "Status"
   - E1: "Hold Reason"
3. **Format headers**:
   - Bold text
   - Blue background (#4285f4)
   - White text color
4. **Add sample data** in row 2:
   - A2: "Sample User"
   - D2: "Active"

#### Sheet 2: "Float Log"
1. **Right-click** sheet tab → **Insert sheet**
2. **Name**: "Float Log"
3. **Add headers** in row 1:
   - A1: "Timestamp"
   - B1: "Staff Name"
   - C1: "Action"
   - D1: "Unit"
   - E1: "Admin"
   - F1: "Notes"
4. **Format headers**:
   - Bold text
   - Red background (#ea4335)
   - White text color

#### Sheet 3: "UserData"
1. **Insert sheet** named "UserData"
2. **Add headers** in row 1:
   - A1: "Staff Name"
   - B1: "PIN Hash"
   - C1: "Created Date"
   - D1: "Last Login"
3. **Add sample user** in row 2:
   - A2: "Sample User"
   - B2: "81dc9bdb52d04dc20036dbd8313ed055" (hash for "1234")
   - C2: =TODAY()
4. **Hide this sheet**: Right-click tab → **Hide sheet**

#### Sheet 4: "Settings"
1. **Insert sheet** named "Settings"
2. **Add configuration** in column A:
   - A1: "ADMIN_PASSWORD"
   - A2: "FloatAdmin2024"
   - A3: "SYSTEM_VERSION"
   - A4: "1.0.0"
   - A5: "LAST_BACKUP"
   - A6: =TODAY()
3. **Hide this sheet**: Right-click tab → **Hide sheet**

### Step 3: Add Buttons
1. **Go to** "Float Queue" sheet
2. **Insert** → **Drawing**
3. **Create** a button:
   - Add text box with "FLOAT"
   - Style with blue background
   - **Save and Close**
4. **Click** the button → **Assign script** → `showFloatDialog`
5. **Repeat** for "Admin Panel" button → assign `showAdminPanel`

### Step 4: Set Up Apps Script
1. **Extensions** → **Apps Script**
2. **Delete** default code in `Code.gs`
3. **Copy and paste** code from `/src/Code.gs`
4. **Add new files** for each script:
   - Click **+** next to "Files"
   - **Script** → Name it (e.g., "PINManager")
   - **Paste** corresponding code
5. **Save** all files

### Step 5: Configure Triggers and Permissions
1. **Click** **Triggers** (clock icon)
2. **Add Trigger**:
   - Function: `onEdit`
   - Event source: "From spreadsheet"
   - Event type: "On edit"
3. **Save** and **authorize** when prompted
4. **Review permissions** carefully before accepting

### Step 6: Protect Sheets
1. **Right-click** "UserData" sheet tab → **Protect sheet**
2. **Set permissions**: "Only you"
3. **Repeat** for "Settings" sheet
4. **For Float Queue**: Protect specific ranges (headers, buttons)

## ⚙️ Initial Configuration

### Step 1: Test Sample User
1. **Click** "FLOAT" button
2. **Enter PIN**: 1234
3. **Select** any unit from dropdown
4. **Verify** success message appears
5. **Check** that queue updates correctly

### Step 2: Access Admin Panel
1. **Click** "Admin Panel" button
2. **Enter password**: FloatAdmin2024
3. **Enter** your name as admin
4. **Verify** admin menu appears

### Step 3: Add Real Users
1. **In admin panel**, select "Add New User"
2. **Enter** staff member's full name
3. **Write down** the generated PIN immediately
4. **Give PIN** to staff member privately
5. **Repeat** for all staff

### Step 4: Remove Sample User
1. **In admin panel**, select "Remove User"
2. **Enter**: Sample User
3. **Confirm** removal

## 🔐 Security Configuration

### Step 1: Change Default Passwords
1. **Go to** "Settings" sheet (unhide if needed)
2. **Change** A2 from "FloatAdmin2024" to your new admin password
3. **Hide** the sheet again

### Step 2: Set Up Sharing
1. **Click** "Share" button (top right)
2. **Add** staff members with "Editor" access
3. **Consider** "Commenter" access for view-only users
4. **Set** link sharing to "Restricted"

### Step 3: Configure Notifications
1. **Tools** → **Notification rules**
2. **Set up** email notifications for changes
3. **Choose** appropriate frequency

## 📱 Mobile Setup

### Step 1: Install Google Sheets App
1. **Download** Google Sheets app on mobile devices
2. **Sign in** with same Google account
3. **Open** Float Tracker System sheet

### Step 2: Test Mobile Functionality
1. **Verify** buttons work on mobile
2. **Test** PIN entry and unit selection
3. **Check** that queue updates properly

## ✅ Verification Checklist

### Before Going Live:
- [ ] All 4 sheets created with proper structure
- [ ] Apps Script code imported and saved
- [ ] Triggers configured and authorized
- [ ] FLOAT and Admin Panel buttons work
- [ ] Sample user can float successfully (PIN: 1234)
- [ ] Admin panel accessible with password
- [ ] User creation generates unique PINs
- [ ] Color coding works (highlighting next in queue)
- [ ] Logging captures actions in Float Log sheet
- [ ] Protected sheets are hidden and secured
- [ ] Mobile access works properly

### Apps Script Functions to Test:
- [ ] `showFloatDialog()` - FLOAT button
- [ ] `showAdminPanel()` - Admin Panel button
- [ ] `processFloat()` - Float submission
- [ ] `addNewUser()` - User creation
- [ ] `removeUser()` - User removal
- [ ] `resetUserPIN()` - PIN reset
- [ ] `updateQueueColors()` - Visual indicators

## 🔧 Troubleshooting

### Common Issues

**"Script authorization required"**
- **Solution**: Click "Review permissions" and authorize the script
- **Note**: Some organizations may require admin approval

**"Buttons don't work"**
- **Solution**: Check script assignment in button properties
- **Verify**: Apps Script functions are saved and named correctly

**"Can't see UserData or Settings sheets"**
- **Solution**: This is normal - they should be hidden for security
- **To unhide**: Right-click sheet tabs area → "Unhide"

**"PIN validation fails"**
- **Solution**: Check UserData sheet has correct PIN hashes
- **Use**: Admin Panel to reset user PIN

**"Colors don't update"**
- **Solution**: Check onEdit trigger is properly configured
- **Manual**: Run `updateQueueColors()` from Apps Script editor

**"Mobile buttons don't work"**
- **Solution**: Ensure Google Sheets app is updated
- **Alternative**: Use mobile web browser instead of app

## 📞 Support

### For Technical Issues:
- Check [User Guide](docs/user-guide.md)
- Check [Admin Guide](docs/admin-guide.md)
- Review Apps Script execution logs
- Contact your G-Workspace administrator

### For Apps Script Issues:
- **View logs**: Apps Script editor → **Executions**
- **Debug**: Use `console.log()` statements
- **Test functions**: Run individual functions in editor

## 🎉 Installation Complete

Your Google Sheets Float Tracker System is now ready for use!

### Remember to:
- **Distribute** user guides to staff
- **Train** administrators on admin functions
- **Set up** regular backup procedures (Google handles this automatically)
- **Monitor** system usage through logs
- **Update** passwords periodically

### Default Credentials for Initial Setup:
- **Sample User PIN**: 1234
- **Admin Password**: FloatAdmin2024

**⚠️ Change these immediately after installation!**

---

**Need help? Check the [documentation](docs/) folder for detailed guides.**
