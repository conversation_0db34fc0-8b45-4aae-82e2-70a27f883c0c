# Float Tracker System - Google Sheets Version

A web-based staff floating management system built with Google Sheets and Apps Script. Ensures fair rotation through a secure, queue-based approach with complete audit trails.

## 🌐 Features

### Core Functionality
- **Fair Queue System**: Automatic rotation ensures everyone gets equal float opportunities
- **PIN Authentication**: Each staff member has a unique 4-digit PIN
- **Real-time Updates**: Queue automatically reorders after each float
- **Visual Indicators**: Color-coded system shows who's next to float
- **Complete Audit Trail**: All actions logged with timestamps
- **Web-based**: Works on any device with internet access

### Administrative Tools
- **User Management**: Add/remove staff, reset PINs
- **Queue Control**: Hold staff for vacation, override floats
- **Reporting**: View float summaries and missed float reports
- **Security**: Password-protected admin functions

### Google Sheets Advantages
- **Real-time Collaboration**: Multiple users can view simultaneously
- **Mobile Friendly**: Works perfectly on phones and tablets
- **Auto-save**: Changes saved automatically to Google Drive
- **Version History**: Built-in change tracking
- **Easy Sharing**: Share with specific users or groups
- **No Software Installation**: Works in any web browser

## 📁 Project Structure

```
google-sheets/
├── README.md                    # This file - project overview
├── INSTALL.md                   # Installation and setup guide
├── Float-Tracker-Template.xlsx # Template to import into Google Sheets
├── src/                        # Apps Script source code
│   ├── Code.gs                 # Main system logic
│   ├── PINManager.gs           # PIN authentication system
│   ├── QueueManager.gs         # Queue management functions
│   ├── AdminPanel.gs           # Administrative functions
│   ├── Logger.gs               # Audit logging system
│   └── UIHelpers.gs            # User interface helpers
└── docs/                       # Documentation
    ├── user-guide.md           # End-user instructions
    ├── admin-guide.md          # Administrator manual
    └── apps-script-guide.md    # Apps Script setup guide
```

## 🚀 Quick Start

### Option 1: Use Pre-built Template (Recommended)
1. **Download** `Float-Tracker-Template.xlsx`
2. **Upload** to Google Drive
3. **Open** with Google Sheets
4. **Follow** the [Installation Guide](INSTALL.md)

### Option 2: Build from Scratch
1. **Create** new Google Sheet
2. **Set up** the sheet structure manually
3. **Import** Apps Script code from `/src/` folder
4. **Configure** triggers and permissions

## 🛠️ System Requirements

### Required
- Google account (personal or G-Workspace)
- Google Sheets access
- Internet connection

### Recommended
- G-Workspace account for advanced features
- Admin access to enable Apps Script

## 🔧 Installation

See [INSTALL.md](INSTALL.md) for complete step-by-step installation instructions.

## 📖 Documentation

- **[Installation Guide](INSTALL.md)** - Complete setup instructions
- **[User Guide](docs/user-guide.md)** - How to use the float system
- **[Admin Guide](docs/admin-guide.md)** - Administrative functions
- **[Apps Script Guide](docs/apps-script-guide.md)** - Technical implementation details

## 🔐 Security Features

### Data Protection
- **PIN Encryption**: All PINs stored using secure hashing
- **Access Controls**: Sheet protection prevents unauthorized edits
- **Admin Authentication**: Password-protected administrative functions
- **Audit Logging**: Complete trail of all system actions

### Default Credentials (Change Immediately!)
- **Sample User PIN**: 1234
- **Admin Password**: FloatAdmin2024

## 🎯 How It Works

### Queue Management
1. **Fair Rotation**: Staff are ordered in a queue
2. **Next Up Indicator**: Color highlighting shows who's next
3. **Automatic Reordering**: After floating, you move to the bottom
4. **Hold System**: Staff can be temporarily removed (vacation, etc.)

### Float Process
1. Staff member clicks "FLOAT" button
2. Enters their unique 4-digit PIN
3. Selects destination unit from dropdown
4. System validates and processes the float
5. Queue automatically updates and saves

## 🔍 Troubleshooting

### Common Issues
- **Script Authorization**: Allow Apps Script permissions when prompted
- **Trigger Setup**: Ensure onEdit triggers are properly configured
- **Sheet Protection**: Verify protected ranges are set correctly
- **PIN Issues**: Use Admin Panel to reset user PINs

### Getting Help
- Check documentation in `/docs/` folder
- Review Apps Script execution logs
- Contact your G-Workspace administrator

## 🆚 Comparison with Microsoft 365 Version

| Feature | Google Sheets | Microsoft 365 |
|---------|---------------|---------------|
| **Platform** | Web Browser | Excel Desktop |
| **Automation** | Apps Script | VBA |
| **Mobile Access** | Excellent | Limited |
| **Real-time Collaboration** | Excellent | Good |
| **Offline Access** | Limited | Yes |
| **Setup Complexity** | Easy | Medium |
| **Security** | Good | Advanced |
| **Cost** | Free/G-Workspace | M365 License |

## 📝 License

This project is provided as-is for internal organizational use. Modify and distribute according to your organization's policies.

## 🤝 Contributing

To modify the system:
1. Edit Apps Script files in `/src/` folder
2. Import changes into Google Sheets Apps Script editor
3. Test thoroughly before deployment
4. Update documentation as needed

---

**Ready to get started? Check out the [Installation Guide](INSTALL.md)!**
