Attribute VB_Name = "LoggingModule"
' ===================================================================
' FLOAT TRACKER SYSTEM - LOGGING MODULE
' Comprehensive logging system for all float activities
' ===================================================================

Option Explicit

' ===================================================================
' MAIN LOGGING FUNCTION
' ===================================================================
Sub LogFloatAction(staffName As String, unitFloatedTo As String, queuePosition As Integer, actionType As String, adminName As String, notes As String)
    On Error GoTo ErrorHandler
    
    Dim logSheet As Worksheet
    Dim nextRow As Long
    Dim logID As Long
    
    Set logSheet = ThisWorkbook.Sheets("Float Log")
    
    ' Find next empty row
    nextRow = logSheet.Cells(logSheet.Rows.Count, 1).End(xlUp).Row + 1
    
    ' Generate unique log ID
    logID = nextRow - 1  ' Subtract 1 for header row
    
    ' Write log entry
    With logSheet
        .Cells(nextRow, 1).Value = logID                    ' Log ID
        .Cells(nextRow, 2).Value = Now()                    ' Current date/time
        .Cells(nextRow, 3).Value = staffName                ' Staff name
        .Cells(nextRow, 4).Value = unitFloatedTo            ' Unit floated to
        .Cells(nextRow, 5).Value = queuePosition            ' Queue position before action
        .Cells(nextRow, 6).Value = actionType               ' Action type
        .Cells(nextRow, 7).Value = adminName                ' Admin name (if applicable)
        .Cells(nextRow, 8).Value = notes                    ' Notes/reason
    End With
    
    ' Format the timestamp
    logSheet.Cells(nextRow, 2).NumberFormat = "mm/dd/yyyy hh:mm:ss"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "Error logging action: " & Err.Description, vbCritical
End Sub

' ===================================================================
' LOG ANALYSIS FUNCTIONS
' ===================================================================
Function GetDailyFloatCount(checkDate As Date) As Integer
    On Error GoTo ErrorHandler
    
    Dim logSheet As Worksheet
    Dim lastRow As Long, i As Long
    Dim count As Integer
    
    Set logSheet = ThisWorkbook.Sheets("Float Log")
    lastRow = logSheet.Cells(logSheet.Rows.Count, 1).End(xlUp).Row
    count = 0
    
    For i = 2 To lastRow
        If DateValue(logSheet.Cells(i, 2).Value) = checkDate Then
            If logSheet.Cells(i, 6).Value = "FLOAT" Or logSheet.Cells(i, 6).Value = "ADMIN_OVERRIDE" Then
                count = count + 1
            End If
        End If
    Next i
    
    GetDailyFloatCount = count
    Exit Function
    
ErrorHandler:
    GetDailyFloatCount = 0
End Function

Function GetLastFloatDate(staffName As String) As Date
    On Error GoTo ErrorHandler
    
    Dim logSheet As Worksheet
    Dim lastRow As Long, i As Long
    Dim lastDate As Date
    
    Set logSheet = ThisWorkbook.Sheets("Float Log")
    lastRow = logSheet.Cells(logSheet.Rows.Count, 1).End(xlUp).Row
    lastDate = DateSerial(1900, 1, 1) ' Default very old date
    
    ' Search from bottom up for most recent float
    For i = lastRow To 2 Step -1
        If logSheet.Cells(i, 3).Value = staffName Then
            If logSheet.Cells(i, 6).Value = "FLOAT" Or logSheet.Cells(i, 6).Value = "ADMIN_OVERRIDE" Then
                lastDate = DateValue(logSheet.Cells(i, 2).Value)
                Exit For
            End If
        End If
    Next i
    
    GetLastFloatDate = lastDate
    Exit Function
    
ErrorHandler:
    GetLastFloatDate = DateSerial(1900, 1, 1)
End Function

' ===================================================================
' LOG MAINTENANCE FUNCTIONS
' ===================================================================
Sub InitializeLogSheet()
    On Error GoTo ErrorHandler
    
    Dim logSheet As Worksheet
    
    ' Check if log sheet exists, create if not
    On Error Resume Next
    Set logSheet = ThisWorkbook.Sheets("Float Log")
    On Error GoTo ErrorHandler
    
    If logSheet Is Nothing Then
        Set logSheet = ThisWorkbook.Sheets.Add
        logSheet.Name = "Float Log"
    End If
    
    ' Set up headers if they don't exist
    If logSheet.Cells(1, 1).Value = "" Then
        With logSheet
            .Cells(1, 1).Value = "Log ID"
            .Cells(1, 2).Value = "Date/Time"
            .Cells(1, 3).Value = "Staff Name"
            .Cells(1, 4).Value = "Unit"
            .Cells(1, 5).Value = "Queue Position"
            .Cells(1, 6).Value = "Action Type"
            .Cells(1, 7).Value = "Admin Name"
            .Cells(1, 8).Value = "Notes/Reason"
            
            ' Format headers
            .Range("A1:H1").Font.Bold = True
            .Range("A1:H1").Interior.Color = RGB(200, 200, 200)
            .Range("A1:H1").Borders.LineStyle = xlContinuous
            
            ' Set column widths
            .Columns("A").ColumnWidth = 8   ' Log ID
            .Columns("B").ColumnWidth = 18  ' Date/Time
            .Columns("C").ColumnWidth = 15  ' Staff Name
            .Columns("D").ColumnWidth = 12  ' Unit
            .Columns("E").ColumnWidth = 12  ' Queue Position
            .Columns("F").ColumnWidth = 18  ' Action Type
            .Columns("G").ColumnWidth = 12  ' Admin Name
            .Columns("H").ColumnWidth = 30  ' Notes/Reason
        End With
    End If
    
    Exit Sub
    
ErrorHandler:
    MsgBox "Error initializing log sheet: " & Err.Description, vbCritical
End Sub

Sub ProtectLogSheet()
    On Error GoTo ErrorHandler
    
    Dim logSheet As Worksheet
    Set logSheet = ThisWorkbook.Sheets("Float Log")
    
    ' Unprotect first in case it's already protected
    logSheet.Unprotect Password:=USERDATA_PASSWORD
    
    ' Protect the sheet from manual editing but allow VBA to write
    logSheet.Protect Password:=USERDATA_PASSWORD, _
                    DrawingObjects:=True, _
                    Contents:=True, _
                    Scenarios:=True, _
                    UserInterfaceOnly:=True, _
                    AllowFormattingCells:=False, _
                    AllowFormattingColumns:=False, _
                    AllowFormattingRows:=False, _
                    AllowInsertingColumns:=False, _
                    AllowInsertingRows:=False, _
                    AllowInsertingHyperlinks:=False, _
                    AllowDeletingColumns:=False, _
                    AllowDeletingRows:=False, _
                    AllowSorting:=False, _
                    AllowFiltering:=True, _
                    AllowUsingPivotTables:=False
    
    Exit Sub
    
ErrorHandler:
    MsgBox "Error protecting log sheet: " & Err.Description, vbCritical
End Sub

' ===================================================================
' LOG REPORTING FUNCTIONS
' ===================================================================
Sub ShowFloatSummary()
    On Error GoTo ErrorHandler
    
    Dim logSheet As Worksheet
    Dim lastRow As Long, i As Long
    Dim staffCounts As Object
    Dim staffName As String
    Dim summary As String
    Dim totalFloats As Integer
    
    Set logSheet = ThisWorkbook.Sheets("Float Log")
    Set staffCounts = CreateObject("Scripting.Dictionary")
    lastRow = logSheet.Cells(logSheet.Rows.Count, 1).End(xlUp).Row
    totalFloats = 0
    
    ' Count floats per staff member
    For i = 2 To lastRow
        If logSheet.Cells(i, 6).Value = "FLOAT" Or logSheet.Cells(i, 6).Value = "ADMIN_OVERRIDE" Then
            staffName = logSheet.Cells(i, 3).Value
            If staffCounts.Exists(staffName) Then
                staffCounts(staffName) = staffCounts(staffName) + 1
            Else
                staffCounts.Add staffName, 1
            End If
            totalFloats = totalFloats + 1
        End If
    Next i
    
    ' Build summary
    summary = "FLOAT SUMMARY REPORT" & vbCrLf & vbCrLf
    summary = summary & "Total Floats: " & totalFloats & vbCrLf & vbCrLf
    summary = summary & "Floats per Staff Member:" & vbCrLf
    
    Dim key As Variant
    For Each key In staffCounts.Keys
        summary = summary & key & ": " & staffCounts(key) & vbCrLf
    Next key
    
    MsgBox summary, vbInformation, "Float Summary"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "Error generating summary: " & Err.Description, vbCritical
End Sub

Sub ShowMissedFloats()
    On Error GoTo ErrorHandler
    
    Dim logSheet As Worksheet
    Dim lastRow As Long, i As Long
    Dim missedList As String
    Dim missedCount As Integer
    
    Set logSheet = ThisWorkbook.Sheets("Float Log")
    lastRow = logSheet.Cells(logSheet.Rows.Count, 1).End(xlUp).Row
    missedCount = 0
    
    missedList = "UNCORRECTED MISSED FLOATS" & vbCrLf & vbCrLf
    
    ' Find missed floats that haven't been corrected
    For i = 2 To lastRow
        If logSheet.Cells(i, 6).Value = "FLOAT_MISSED" Then
            ' Check if this missed float was later corrected
            If Not MissedFloatWasCorrected(logSheet.Cells(i, 3).Value, DateValue(logSheet.Cells(i, 2).Value)) Then
                missedList = missedList & logSheet.Cells(i, 3).Value & " - " & Format(logSheet.Cells(i, 2).Value, "mm/dd/yyyy") & vbCrLf
                missedCount = missedCount + 1
            End If
        End If
    Next i
    
    If missedCount = 0 Then
        missedList = missedList & "No uncorrected missed floats found."
    Else
        missedList = missedList & vbCrLf & "Total: " & missedCount & " uncorrected missed floats"
    End If
    
    MsgBox missedList, vbInformation, "Missed Floats Report"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "Error generating missed floats report: " & Err.Description, vbCritical
End Sub

Function MissedFloatWasCorrected(staffName As String, missedDate As Date) As Boolean
    Dim logSheet As Worksheet
    Dim lastRow As Long, i As Long
    
    Set logSheet = ThisWorkbook.Sheets("Float Log")
    lastRow = logSheet.Cells(logSheet.Rows.Count, 1).End(xlUp).Row
    
    For i = 2 To lastRow
        If logSheet.Cells(i, 6).Value = "FLOAT_MISSED_CORRECTED" And _
           logSheet.Cells(i, 3).Value = staffName And _
           DateValue(logSheet.Cells(i, 2).Value) = missedDate Then
            MissedFloatWasCorrected = True
            Exit Function
        End If
    Next i
    
    MissedFloatWasCorrected = False
End Function
