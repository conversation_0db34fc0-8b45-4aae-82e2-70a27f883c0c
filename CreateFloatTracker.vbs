' ===================================================================
' FLOAT TRACKER SYSTEM - EXCEL FILE CREATOR
' VBScript to create the complete Excel workbook with all modules
' ===================================================================

Option Explicit

Dim xlApp, xlWorkbook, xlWorksheet
Dim fso, folder, file
Dim moduleContent, formContent

' Create Excel application
Set xlApp = CreateObject("Excel.Application")
xlApp.Visible = True
xlApp.DisplayAlerts = False

' Create new workbook
Set xlWorkbook = xlApp.Workbooks.Add

' Create file system object
Set fso = CreateObject("Scripting.FileSystemObject")

' Get current directory
folder = fso.GetParentFolderName(WScript.ScriptFullName)

WScript.Echo "Creating Float Tracker System..."
WScript.Echo "Working directory: " & folder

' ===================================================================
' CREATE WORKSHEETS
' ===================================================================

' Rename default sheet to Float Queue
xlWorkbook.Sheets(1).Name = "Float Queue"

' Add additional sheets
xlWorkbook.Sheets.Add.Name = "Float Log"
xlWorkbook.Sheets.Add.Name = "UserData" 
xlWorkbook.Sheets.Add.Name = "Settings"

WScript.Echo "Created worksheets..."

' ===================================================================
' IMPORT VBA MODULES
' ===================================================================

' Import main modules
If fso.FileExists(folder & "\FloatTracker_MainModule.bas") Then
    xlWorkbook.VBProject.VBComponents.Import folder & "\FloatTracker_MainModule.bas"
    WScript.Echo "Imported MainModule"
End If

If fso.FileExists(folder & "\FloatTracker_PINModule.bas") Then
    xlWorkbook.VBProject.VBComponents.Import folder & "\FloatTracker_PINModule.bas"
    WScript.Echo "Imported PINModule"
End If

If fso.FileExists(folder & "\FloatTracker_LoggingModule.bas") Then
    xlWorkbook.VBProject.VBComponents.Import folder & "\FloatTracker_LoggingModule.bas"
    WScript.Echo "Imported LoggingModule"
End If

If fso.FileExists(folder & "\FloatTracker_AdminModule.bas") Then
    xlWorkbook.VBProject.VBComponents.Import folder & "\FloatTracker_AdminModule.bas"
    WScript.Echo "Imported AdminModule"
End If

If fso.FileExists(folder & "\FloatTracker_WorksheetModule.bas") Then
    xlWorkbook.VBProject.VBComponents.Import folder & "\FloatTracker_WorksheetModule.bas"
    WScript.Echo "Imported WorksheetModule"
End If

' Import ThisWorkbook class
If fso.FileExists(folder & "\ThisWorkbook.cls") Then
    ' Remove default ThisWorkbook and import custom one
    xlWorkbook.VBProject.VBComponents.Remove xlWorkbook.VBProject.VBComponents("ThisWorkbook")
    xlWorkbook.VBProject.VBComponents.Import folder & "\ThisWorkbook.cls"
    WScript.Echo "Imported ThisWorkbook class"
End If

' Import UserForm
If fso.FileExists(folder & "\FloatForm.frm") Then
    xlWorkbook.VBProject.VBComponents.Import folder & "\FloatForm.frm"
    WScript.Echo "Imported FloatForm"
End If

WScript.Echo "Imported all VBA modules..."

' ===================================================================
' SETUP WORKSHEETS
' ===================================================================

' Setup Float Queue sheet
Set xlWorksheet = xlWorkbook.Sheets("Float Queue")
With xlWorksheet
    .Cells(1, 1).Value = "Staff Name"
    .Cells(1, 2).Value = "Last Floated"
    .Cells(1, 3).Value = "Float Unit"
    .Cells(1, 4).Value = "Status"
    .Cells(1, 5).Value = "Hold Reason"
    
    ' Format headers
    .Range("A1:E1").Font.Bold = True
    .Range("A1:E1").Font.Size = 12
    .Range("A1:E1").Interior.Color = RGB(100, 150, 200)
    .Range("A1:E1").Font.Color = RGB(255, 255, 255)
    
    ' Set column widths
    .Columns("A").ColumnWidth = 20
    .Columns("B").ColumnWidth = 18
    .Columns("C").ColumnWidth = 15
    .Columns("D").ColumnWidth = 12
    .Columns("E").ColumnWidth = 25
    
    ' Add sample data
    .Cells(2, 1).Value = "Sample User"
    .Cells(2, 4).Value = "Active"
End With

' Setup Float Log sheet
Set xlWorksheet = xlWorkbook.Sheets("Float Log")
With xlWorksheet
    .Cells(1, 1).Value = "Log ID"
    .Cells(1, 2).Value = "Date/Time"
    .Cells(1, 3).Value = "Staff Name"
    .Cells(1, 4).Value = "Unit"
    .Cells(1, 5).Value = "Queue Position"
    .Cells(1, 6).Value = "Action Type"
    .Cells(1, 7).Value = "Admin Name"
    .Cells(1, 8).Value = "Notes/Reason"
    
    ' Format headers
    .Range("A1:H1").Font.Bold = True
    .Range("A1:H1").Interior.Color = RGB(150, 100, 100)
    .Range("A1:H1").Font.Color = RGB(255, 255, 255)
    
    ' Set column widths
    .Columns("A").ColumnWidth = 8
    .Columns("B").ColumnWidth = 18
    .Columns("C").ColumnWidth = 15
    .Columns("D").ColumnWidth = 12
    .Columns("E").ColumnWidth = 12
    .Columns("F").ColumnWidth = 18
    .Columns("G").ColumnWidth = 12
    .Columns("H").ColumnWidth = 30
End With

' Setup UserData sheet
Set xlWorksheet = xlWorkbook.Sheets("UserData")
With xlWorksheet
    .Cells(1, 1).Value = "Staff Name"
    .Cells(1, 2).Value = "Encoded PIN"
    .Cells(1, 3).Value = "Date Created"
    .Cells(1, 4).Value = "Created By"
    .Cells(1, 5).Value = "Status"
    
    ' Format headers
    .Range("A1:E1").Font.Bold = True
    .Range("A1:E1").Interior.Color = RGB(200, 200, 200)
    
    ' Set column widths
    .Columns("A").ColumnWidth = 20
    .Columns("B").ColumnWidth = 15
    .Columns("C").ColumnWidth = 18
    .Columns("D").ColumnWidth = 15
    .Columns("E").ColumnWidth = 12
    
    ' Add sample user (PIN: 1234)
    .Cells(2, 1).Value = "Sample User"
    .Cells(2, 2).Value = "5678"  ' Encoded version of 1234
    .Cells(2, 3).Value = Now()
    .Cells(2, 4).Value = "System"
    .Cells(2, 5).Value = "Active"
    
    ' Hide sheet
    .Visible = -2  ' xlSheetVeryHidden
End With

' Setup Settings sheet
Set xlWorkbook.Sheets("Settings")
With xlWorkbook.Sheets("Settings")
    .Cells(1, 1).Value = "Float Tracker Configuration"
    .Cells(1, 1).Font.Bold = True
    .Cells(1, 1).Font.Size = 14
    
    .Cells(3, 1).Value = "Available Units:"
    .Cells(3, 1).Font.Bold = True
    
    ' Hospital-specific units
    .Cells(4, 1).Value = "EW1"
    .Cells(5, 1).Value = "EW2"
    .Cells(6, 1).Value = "ICU"
    .Cells(7, 1).Value = "SR"
    .Cells(8, 1).Value = "NR"
    .Cells(9, 1).Value = "SBU"
    .Cells(10, 1).Value = "CAP"
    
    .Cells(13, 1).Value = "System Information:"
    .Cells(13, 1).Font.Bold = True
    .Cells(14, 1).Value = "Version: 1.0"
    .Cells(15, 1).Value = "Created: " & FormatDateTime(Now(), 2)
    
    ' Hide sheet
    .Visible = 0  ' xlSheetHidden
End With

WScript.Echo "Setup worksheets..."

' ===================================================================
' ADD BUTTONS
' ===================================================================

' Add FLOAT button to Float Queue sheet
Set xlWorksheet = xlWorkbook.Sheets("Float Queue")
Dim floatButton
Set floatButton = xlWorksheet.Buttons.Add(50, 50, 100, 30)
floatButton.Text = "FLOAT"
floatButton.OnAction = "FloatButton_Click"

' Add Admin Panel button
Dim adminButton
Set adminButton = xlWorksheet.Buttons.Add(200, 50, 120, 30)
adminButton.Text = "Admin Panel"
adminButton.OnAction = "ShowAdminPanel"

WScript.Echo "Added buttons..."

' ===================================================================
' SAVE WORKBOOK
' ===================================================================

' Save as macro-enabled workbook
xlWorkbook.SaveAs folder & "\FloatTracker.xlsm", 52  ' xlOpenXMLWorkbookMacroEnabled

WScript.Echo "Saved as FloatTracker.xlsm"

' ===================================================================
' CLEANUP
' ===================================================================

xlApp.DisplayAlerts = True
xlApp.Quit

Set xlWorksheet = Nothing
Set xlWorkbook = Nothing
Set xlApp = Nothing
Set fso = Nothing

WScript.Echo ""
WScript.Echo "Float Tracker System created successfully!"
WScript.Echo "File: " & folder & "\FloatTracker.xlsm"
WScript.Echo ""
WScript.Echo "Next steps:"
WScript.Echo "1. Open FloatTracker.xlsm"
WScript.Echo "2. Enable macros when prompted"
WScript.Echo "3. System will initialize automatically"
WScript.Echo "4. Use Admin Panel to add users"
WScript.Echo ""
WScript.Echo "Default sample user PIN: 1234"
WScript.Echo "Admin password: FloatAdmin2024"
