VERSION 5.00
Begin {C62A69F0-16DC-11CE-9E98-00AA00574A4F} FloatForm 
   Caption         =   "Float Request"
   ClientHeight    =   4200
   ClientLeft      =   45
   ClientTop       =   375
   ClientWidth     =   4800
   OleObjectBlob   =   "FloatForm.frx":0000
   StartUpPosition =   1  'CenterOwner
End
Attribute VB_Name = "FloatForm"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False

' ===================================================================
' FLOAT FORM - USER INTERFACE FOR FLOAT REQUESTS
' ===================================================================

Option Explicit

' Form properties
Public ValidSubmission As Boolean
Public StaffName As String
Public SelectedUnit As String

Private Sub UserForm_Initialize()
    ' Initialize form
    ValidSubmission = False
    StaffName = ""
    SelectedUnit = ""
    
    ' Set up form appearance
    Me.Caption = "Float Request - Enter Your Information"
    
    ' Populate unit dropdown
    Call PopulateUnits()
    
    ' Set focus to PIN textbox
    txtPIN.SetFocus
End Sub

Private Sub PopulateUnits()
    ' Clear existing items
    cmbUnit.Clear
    
    ' Add available units for this hospital
    cmbUnit.AddItem "EW1"
    cmbUnit.AddItem "EW2"
    cmbUnit.AddItem "ICU"
    cmbUnit.AddItem "SR"
    cmbUnit.AddItem "NR"
    cmbUnit.AddItem "SBU"
    cmbUnit.AddItem "CAP"
    
    ' Set default selection
    cmbUnit.ListIndex = 0
End Sub

Private Sub btnOK_Click()
    On Error GoTo ErrorHandler
    
    Dim enteredPIN As String
    Dim selectedUnitName As String
    
    ' Get entered values
    enteredPIN = Trim(txtPIN.Text)
    selectedUnitName = cmbUnit.Text
    
    ' Validate inputs
    If enteredPIN = "" Then
        MsgBox "Please enter your PIN.", vbExclamation, "PIN Required"
        txtPIN.SetFocus
        Exit Sub
    End If
    
    If Len(enteredPIN) <> 4 Then
        MsgBox "PIN must be exactly 4 digits.", vbExclamation, "Invalid PIN"
        txtPIN.SetFocus
        Exit Sub
    End If
    
    If Not IsNumeric(enteredPIN) Then
        MsgBox "PIN must contain only numbers.", vbExclamation, "Invalid PIN"
        txtPIN.SetFocus
        Exit Sub
    End If
    
    If selectedUnitName = "" Then
        MsgBox "Please select a unit.", vbExclamation, "Unit Required"
        cmbUnit.SetFocus
        Exit Sub
    End If
    
    ' Find staff name by PIN validation
    StaffName = FindStaffByPIN(enteredPIN)
    
    If StaffName = "" Then
        MsgBox "Invalid PIN. Please check your PIN and try again.", vbCritical, "Authentication Failed"
        txtPIN.Text = ""
        txtPIN.SetFocus
        Exit Sub
    End If
    
    ' Check if staff is active
    If Not IsStaffActive(StaffName) Then
        MsgBox "Your account is currently inactive or on hold. Please contact an administrator.", vbExclamation, "Account Inactive"
        Exit Sub
    End If
    
    ' Set return values
    SelectedUnit = selectedUnitName
    ValidSubmission = True
    
    ' Close form
    Me.Hide
    
    Exit Sub
    
ErrorHandler:
    MsgBox "Error processing float request: " & Err.Description, vbCritical
    ValidSubmission = False
End Sub

Private Sub btnCancel_Click()
    ValidSubmission = False
    StaffName = ""
    SelectedUnit = ""
    Me.Hide
End Sub

Private Sub txtPIN_KeyPress(KeyAscii As Integer)
    ' Only allow numbers
    If KeyAscii < 48 Or KeyAscii > 57 Then
        If KeyAscii <> 8 Then ' Allow backspace
            KeyAscii = 0
        End If
    End If
    
    ' Limit to 4 characters
    If Len(txtPIN.Text) >= 4 And KeyAscii <> 8 Then
        KeyAscii = 0
    End If
End Sub

Private Sub txtPIN_Change()
    ' Auto-advance to unit selection when 4 digits entered
    If Len(txtPIN.Text) = 4 Then
        cmbUnit.SetFocus
    End If
End Sub

Private Function FindStaffByPIN(pin As String) As String
    On Error GoTo ErrorHandler
    
    Dim userDataSheet As Worksheet
    Dim lastRow As Long, i As Long
    Dim storedPIN As String
    
    Set userDataSheet = ThisWorkbook.Sheets("UserData")
    lastRow = userDataSheet.Cells(userDataSheet.Rows.Count, 1).End(xlUp).Row
    
    For i = 2 To lastRow
        If userDataSheet.Cells(i, 5).Value = "Active" Then
            storedPIN = DecodePIN(userDataSheet.Cells(i, 2).Value)
            If storedPIN = pin Then
                FindStaffByPIN = userDataSheet.Cells(i, 1).Value
                Exit Function
            End If
        End If
    Next i
    
    FindStaffByPIN = ""
    Exit Function
    
ErrorHandler:
    FindStaffByPIN = ""
End Function

Private Sub UserForm_QueryClose(Cancel As Integer, CloseMode As Integer)
    ' Handle X button click
    If CloseMode = 0 Then ' User clicked X
        ValidSubmission = False
        StaffName = ""
        SelectedUnit = ""
    End If
End Sub
