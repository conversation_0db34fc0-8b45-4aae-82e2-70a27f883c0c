Attribute VB_Name = "AdminModule"
' ===================================================================
' FLOAT TRACKER SYSTEM - ADMIN MODULE
' Administrative functions and overrides
' ===================================================================

Option Explicit

' Global admin session variable
Public CurrentAdminUser As String

' ===================================================================
' ADMIN AUTHENTICATION
' ===================================================================
Function AdminLogin() As Boolean
    On Error GoTo ErrorHandler
    
    Dim adminPassword As String
    Dim adminName As String
    
    adminPassword = InputBox("Enter Admin Password:", "Admin Access Required")
    
    If adminPassword = "" Then
        AdminLogin = False
        Exit Function
    End If
    
    If adminPassword = ADMIN_PASSWORD Then
        adminName = InputBox("Enter your admin name for logging:", "Admin Identification")
        If adminName <> "" Then
            CurrentAdminUser = adminName
            AdminLogin = True
            MsgBox "Admin access granted. Welcome, " & adminName & "!", vbInformation
        Else
            AdminLogin = False
        End If
    Else
        MsgBox "Invalid admin password!", vbCritical
        AdminLogin = False
    End If
    
    Exit Function
    
ErrorHandler:
    AdminLogin = False
    MsgBox "Error during admin login: " & Err.Description, vbCritical
End Function

Sub AdminLogout()
    CurrentAdminUser = ""
    MsgBox "Admin logged out successfully.", vbInformation
End Sub

Function GetCurrentAdmin() As String
    If CurrentAdminUser = "" Then
        GetCurrentAdmin = "Unknown Admin"
    Else
        GetCurrentAdmin = CurrentAdminUser
    End If
End Function

' ===================================================================
' ADMIN PANEL
' ===================================================================
Sub ShowAdminPanel()
    On Error GoTo ErrorHandler
    
    If Not AdminLogin() Then Exit Sub
    
    Dim choice As String
    Dim menuOptions As String
    
    Do
        menuOptions = "FLOAT TRACKER - ADMIN PANEL" & vbCrLf & vbCrLf & _
                     "Select an option:" & vbCrLf & vbCrLf & _
                     "1 - Override Float (Float someone without PIN)" & vbCrLf & _
                     "2 - Add New User" & vbCrLf & _
                     "3 - Remove User" & vbCrLf & _
                     "4 - Reset User PIN" & vbCrLf & _
                     "5 - Hold Staff (Vacation/Unavailable)" & vbCrLf & _
                     "6 - Release Staff from Hold" & vbCrLf & _
                     "7 - Reset Queue Order" & vbCrLf & _
                     "8 - View Float Summary" & vbCrLf & _
                     "9 - View Missed Floats" & vbCrLf & _
                     "0 - Exit Admin Panel" & vbCrLf & vbCrLf & _
                     "Enter choice (0-9):"
        
        choice = InputBox(menuOptions, "Admin Panel")
        
        Select Case choice
            Case "1"
                Call AdminOverrideFloat()
            Case "2"
                Call AddNewUser()
            Case "3"
                Call RemoveUser()
            Case "4"
                Call ResetUserPIN()
            Case "5"
                Call SetStaffHold()
            Case "6"
                Call ReleaseStaffHold()
            Case "7"
                Call ResetQueueOrder()
            Case "8"
                Call ShowFloatSummary()
            Case "9"
                Call ShowMissedFloats()
            Case "0", ""
                Exit Do
            Case Else
                MsgBox "Invalid choice. Please enter 0-9.", vbExclamation
        End Select
        
    Loop
    
    Call AdminLogout()
    
    Exit Sub
    
ErrorHandler:
    MsgBox "Error in admin panel: " & Err.Description, vbCritical
End Sub

' ===================================================================
' ADMIN OVERRIDE FUNCTIONS
' ===================================================================
Sub AdminOverrideFloat()
    On Error GoTo ErrorHandler
    
    Dim staffName As String
    Dim selectedUnit As String
    Dim availableUnits As String
    Dim ws As Worksheet
    Dim lastRow As Long, i As Long
    Dim staffList As String
    
    Set ws = ThisWorkbook.Sheets("Float Queue")
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
    
    ' Build staff list
    staffList = "Available Staff:" & vbCrLf
    For i = 2 To lastRow
        If ws.Cells(i, 1).Value <> "" And ws.Cells(i, 4).Value = "Active" Then
            staffList = staffList & "• " & ws.Cells(i, 1).Value & vbCrLf
        End If
    Next i
    
    ' Get staff selection
    MsgBox staffList, vbInformation, "Staff List"
    staffName = InputBox("Enter staff name to float:", "Admin Override Float")
    
    If staffName = "" Then Exit Sub
    
    ' Validate staff exists and is active
    If Not IsStaffActive(staffName) Then
        MsgBox "Staff member '" & staffName & "' not found or not active!", vbExclamation
        Exit Sub
    End If
    
    ' Get unit selection
    availableUnits = GetAvailableUnits()
    MsgBox "Available Units:" & vbCrLf & availableUnits, vbInformation, "Unit List"
    selectedUnit = InputBox("Enter unit name:", "Select Unit")
    
    If selectedUnit = "" Then Exit Sub
    
    ' Confirm override
    If MsgBox("Confirm admin override float:" & vbCrLf & vbCrLf & _
              "Staff: " & staffName & vbCrLf & _
              "Unit: " & selectedUnit & vbCrLf & _
              "Admin: " & GetCurrentAdmin(), _
              vbYesNo + vbQuestion, "Confirm Override") = vbYes Then
        
        ' Process the override float
        Call ProcessFloat(staffName, selectedUnit, True) ' True = admin override
        
        MsgBox "Admin override completed successfully!", vbInformation
    End If
    
    Exit Sub
    
ErrorHandler:
    MsgBox "Error in admin override: " & Err.Description, vbCritical
End Sub

Function IsStaffActive(staffName As String) As Boolean
    Dim ws As Worksheet
    Dim lastRow As Long, i As Long
    
    Set ws = ThisWorkbook.Sheets("Float Queue")
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
    
    For i = 2 To lastRow
        If ws.Cells(i, 1).Value = staffName And ws.Cells(i, 4).Value = "Active" Then
            IsStaffActive = True
            Exit Function
        End If
    Next i
    
    IsStaffActive = False
End Function

Function GetAvailableUnits() As String
    ' Hospital-specific units for float assignments
    GetAvailableUnits = "• EW1" & vbCrLf & _
                       "• EW2" & vbCrLf & _
                       "• ICU" & vbCrLf & _
                       "• SR" & vbCrLf & _
                       "• NR" & vbCrLf & _
                       "• SBU" & vbCrLf & _
                       "• CAP"
End Function

' ===================================================================
' STAFF HOLD MANAGEMENT
' ===================================================================
Sub SetStaffHold()
    On Error GoTo ErrorHandler
    
    Dim staffName As String
    Dim reason As String
    Dim ws As Worksheet
    Dim lastRow As Long, i As Long
    
    staffName = InputBox("Enter staff name to put on hold:", "Set Staff Hold")
    If staffName = "" Then Exit Sub
    
    If Not IsStaffActive(staffName) Then
        MsgBox "Staff member '" & staffName & "' not found or already on hold!", vbExclamation
        Exit Sub
    End If
    
    reason = InputBox("Enter reason for hold (e.g., 'Vacation 8/1-8/15'):", "Hold Reason")
    If reason = "" Then reason = "Administrative hold"
    
    ' Update status
    Set ws = ThisWorkbook.Sheets("Float Queue")
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
    
    For i = 2 To lastRow
        If ws.Cells(i, 1).Value = staffName Then
            ws.Cells(i, 4).Value = "On Hold"     ' Status
            ws.Cells(i, 5).Value = reason        ' Hold Reason
            Exit For
        End If
    Next i
    
    ' Log the hold action
    Call LogFloatAction(staffName, "", GetQueuePosition(staffName), "QUEUE_HOLD", GetCurrentAdmin(), reason)

    ' Update colors
    Call UpdateQueueColors()

    ' Auto-save after hold action
    Call SafeSave("Hold set: " & staffName)

    MsgBox "Staff member '" & staffName & "' has been placed on hold." & vbCrLf & _
           "Reason: " & reason, vbInformation
    
    Exit Sub
    
ErrorHandler:
    MsgBox "Error setting staff hold: " & Err.Description, vbCritical
End Sub

Sub ReleaseStaffHold()
    On Error GoTo ErrorHandler
    
    Dim staffName As String
    Dim ws As Worksheet
    Dim lastRow As Long, i As Long
    
    ' Show current holds
    Call ShowCurrentHolds()
    
    staffName = InputBox("Enter staff name to release from hold:", "Release Staff Hold")
    If staffName = "" Then Exit Sub
    
    If Not IsStaffOnHold(staffName) Then
        MsgBox "Staff member '" & staffName & "' is not currently on hold!", vbExclamation
        Exit Sub
    End If
    
    ' Update status
    Set ws = ThisWorkbook.Sheets("Float Queue")
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
    
    For i = 2 To lastRow
        If ws.Cells(i, 1).Value = staffName Then
            ws.Cells(i, 4).Value = "Active"      ' Status
            ws.Cells(i, 5).Value = ""            ' Clear hold reason
            Exit For
        End If
    Next i
    
    ' Log the release action
    Call LogFloatAction(staffName, "", GetQueuePosition(staffName), "QUEUE_RELEASE", GetCurrentAdmin(), "Released from hold")

    ' Update colors
    Call UpdateQueueColors()

    ' Auto-save after release action
    Call SafeSave("Hold released: " & staffName)

    MsgBox "Staff member '" & staffName & "' has been released from hold and is now active in the queue.", vbInformation
    
    Exit Sub
    
ErrorHandler:
    MsgBox "Error releasing staff hold: " & Err.Description, vbCritical
End Sub

Sub ShowCurrentHolds()
    Dim ws As Worksheet
    Dim lastRow As Long, i As Long
    Dim holdList As String
    Dim holdCount As Integer
    
    Set ws = ThisWorkbook.Sheets("Float Queue")
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
    holdCount = 0
    
    holdList = "STAFF CURRENTLY ON HOLD:" & vbCrLf & vbCrLf
    
    For i = 2 To lastRow
        If ws.Cells(i, 1).Value <> "" And ws.Cells(i, 4).Value = "On Hold" Then
            holdList = holdList & "• " & ws.Cells(i, 1).Value & " - " & ws.Cells(i, 5).Value & vbCrLf
            holdCount = holdCount + 1
        End If
    Next i
    
    If holdCount = 0 Then
        holdList = holdList & "No staff currently on hold."
    End If
    
    MsgBox holdList, vbInformation, "Current Holds"
End Sub

Function IsStaffOnHold(staffName As String) As Boolean
    Dim ws As Worksheet
    Dim lastRow As Long, i As Long

    Set ws = ThisWorkbook.Sheets("Float Queue")
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row

    For i = 2 To lastRow
        If ws.Cells(i, 1).Value = staffName And ws.Cells(i, 4).Value = "On Hold" Then
            IsStaffOnHold = True
            Exit Function
        End If
    Next i

    IsStaffOnHold = False
End Function

' ===================================================================
' QUEUE RESET FUNCTION
' ===================================================================
Sub ResetQueueOrder()
    On Error GoTo ErrorHandler

    Dim response As VbMsgBoxResult
    Dim ws As Worksheet
    Dim lastRow As Long

    response = MsgBox("Are you sure you want to reset the entire queue order?" & vbCrLf & vbCrLf & _
                     "This will:" & vbCrLf & _
                     "• Randomize the order of all active staff" & vbCrLf & _
                     "• Keep staff on hold in their current positions" & vbCrLf & _
                     "• Log this action for audit purposes" & vbCrLf & vbCrLf & _
                     "This action cannot be undone!", _
                     vbYesNo + vbExclamation, "Confirm Queue Reset")

    If response = vbYes Then
        Set ws = ThisWorkbook.Sheets("Float Queue")
        lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row

        ' Randomize active staff order
        Call RandomizeActiveStaff()

        ' Log the reset
        Call LogFloatAction("ALL_STAFF", "", 0, "QUEUE_RESET", GetCurrentAdmin(), "Queue order reset by admin")

        ' Update colors
        Call UpdateQueueColors()

        ' Force backup save after queue reset
        Call ForceBackupSave()

        MsgBox "Queue order has been reset successfully!" & vbCrLf & _
               "All active staff have been randomly reordered.", vbInformation
    End If

    Exit Sub

ErrorHandler:
    MsgBox "Error resetting queue: " & Err.Description, vbCritical
End Sub

Sub RandomizeActiveStaff()
    Dim ws As Worksheet
    Dim lastRow As Long, i As Long, j As Long
    Dim activeStaff() As Variant
    Dim activeCount As Integer
    Dim tempRow As Variant

    Set ws = ThisWorkbook.Sheets("Float Queue")
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row

    ' Count active staff
    activeCount = 0
    For i = 2 To lastRow
        If ws.Cells(i, 1).Value <> "" And ws.Cells(i, 4).Value = "Active" Then
            activeCount = activeCount + 1
        End If
    Next i

    If activeCount <= 1 Then Exit Sub ' Nothing to randomize

    ' Store active staff data
    ReDim activeStaff(1 To activeCount, 1 To 5)
    j = 1
    For i = 2 To lastRow
        If ws.Cells(i, 1).Value <> "" And ws.Cells(i, 4).Value = "Active" Then
            activeStaff(j, 1) = ws.Cells(i, 1).Value ' Name
            activeStaff(j, 2) = ws.Cells(i, 2).Value ' Last Floated
            activeStaff(j, 3) = ws.Cells(i, 3).Value ' Float Unit
            activeStaff(j, 4) = ws.Cells(i, 4).Value ' Status
            activeStaff(j, 5) = ws.Cells(i, 5).Value ' Hold Reason
            j = j + 1
        End If
    Next i

    ' Shuffle the array (Fisher-Yates shuffle)
    Randomize
    For i = activeCount To 2 Step -1
        j = Int(Rnd() * i) + 1
        ' Swap rows i and j
        For k = 1 To 5
            tempRow = activeStaff(i, k)
            activeStaff(i, k) = activeStaff(j, k)
            activeStaff(j, k) = tempRow
        Next k
    Next i

    ' Write shuffled data back to sheet
    j = 1
    For i = 2 To lastRow
        If ws.Cells(i, 1).Value <> "" And ws.Cells(i, 4).Value = "Active" Then
            ws.Cells(i, 1).Value = activeStaff(j, 1)
            ws.Cells(i, 2).Value = activeStaff(j, 2)
            ws.Cells(i, 3).Value = activeStaff(j, 3)
            ws.Cells(i, 4).Value = activeStaff(j, 4)
            ws.Cells(i, 5).Value = activeStaff(j, 5)
            j = j + 1
        End If
    Next i
End Sub
