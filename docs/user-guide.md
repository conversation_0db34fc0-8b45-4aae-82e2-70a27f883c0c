# Float Tracker System - User Guide

## Overview
The Float Tracker System is a Microsoft Excel-based application that manages staff floating assignments through a secure, queue-based system. It ensures fair rotation while maintaining complete audit trails.

## Getting Started

### First Time Setup
1. Open the `FloatTracker.xlsm` file
2. Enable macros when prompted (required for system functionality)
3. The system will initialize automatically
4. Contact your administrator to get your personal 4-digit PIN

### Main Interface
The main "Float Queue" sheet shows:
- **Staff Name**: All staff members in the float rotation
- **Last Floated**: Date/time of last float assignment
- **Float Unit**: Most recent unit floated to
- **Status**: Active or On Hold
- **Hold Reason**: Why someone is on hold (vacation, etc.)

## How to Submit a Float Request

### Step 1: Click the FLOAT Button
- Located on the main "Float Queue" sheet
- Large blue button at the top of the sheet

### Step 2: Enter Your Information
A popup window will appear asking for:
- **Your 4-digit PIN**: Enter your personal PIN (appears as *****)
- **Unit Selection**: Choose the unit you're floating to from the dropdown

### Step 3: Confirm Your Request
- Click "OK" to submit your float request
- Click "Cancel" to abort the request

### What Happens Next
1. System validates your PIN
2. Your row moves to the bottom of the queue (fair rotation)
3. Your "Last Floated" time is updated
4. Your "Float Unit" is updated
5. The action is logged for audit purposes
6. Queue colors are updated

## Understanding the Color System

### 🟠 Orange Highlighting
- **Meaning**: You are next up to float
- **Action**: You should be the next person to submit a float request

### 🟢 Green Highlighting  
- **Meaning**: You are in the normal queue rotation
- **Action**: Wait your turn - others are ahead of you

### 🔴 Red Highlighting
- **Meaning**: You were next to float but someone else floated before you
- **Action**: This indicates you were "skipped" in rotation
- **Note**: Red highlighting is automatically corrected if multiple people float the same day

### ⚫ Gray Highlighting
- **Meaning**: You are currently on hold (vacation, unavailable, etc.)
- **Action**: Contact an administrator to be released from hold

## Troubleshooting

### "Invalid PIN" Error
- Double-check you entered your 4-digit PIN correctly
- Make sure you're using numbers only
- Contact administrator if you've forgotten your PIN

### "Account Inactive" Error
- Your account may be on hold or deactivated
- Contact an administrator for assistance

### Button Not Working
- Ensure macros are enabled in Excel
- Try closing and reopening the file
- Contact IT support if problems persist

## Important Notes

### Security
- **Never share your PIN** with other staff members
- PINs are unique and confidential to each person
- All float actions are logged with timestamps

### Fair Rotation
- The system ensures fair rotation by moving floated staff to the bottom
- Queue position is automatically managed
- Administrators can override for emergencies

### Data Integrity
- Do not manually edit the queue data
- Use only the FLOAT button to submit requests
- The log sheet is protected and cannot be modified

## Getting Help

### For PIN Issues
- Forgotten PIN: Contact administrator for reset
- PIN not working: Verify with administrator

### For Technical Issues
- Button not responding: Check macro settings
- System errors: Contact IT support
- File corruption: Restore from backup

### For Administrative Issues
- Hold status: Contact administrator
- Queue position questions: Contact administrator
- Policy questions: Contact supervisor

## Contact Information
- **System Administrator**: [Your Admin Contact]
- **IT Support**: [Your IT Contact]
- **Supervisor**: [Your Supervisor Contact]

---

*Float Tracker System v1.0 - For internal use only*
