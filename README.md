# Float Tracker System

A Microsoft Excel-based staff floating management system that ensures fair rotation through a secure, queue-based approach with complete audit trails.

## 🚀 Quick Start

1. **Download** all files to a single folder
2. **Run** `CreateFloatTracker.vbs` for automated installation
3. **Open** `FloatTracker.xlsm` and enable macros
4. **Follow** the [Installation Guide](INSTALL.md) for detailed setup

## 📋 Features

### Core Functionality
- **Fair Queue System**: Automatic rotation ensures everyone gets equal float opportunities
- **Secure PIN Authentication**: Each staff member has a unique 4-digit PIN
- **Real-time Updates**: Queue automatically reorders after each float
- **Visual Indicators**: Color-coded system shows who's next to float
- **Complete Audit Trail**: All actions logged with timestamps

### Administrative Tools
- **User Management**: Add/remove staff, reset PINs
- **Queue Control**: Hold staff for vacation, override floats
- **Reporting**: View float summaries and missed float reports
- **Security**: Password-protected admin functions

### Technical Features
- **Auto-save**: Automatic saving after each operation (OneDrive compatible)
- **Data Protection**: Hidden worksheets protect sensitive data
- **Error Handling**: Comprehensive validation and error recovery
- **Multi-user Support**: Designed for shared network/cloud deployment

## 📁 Project Structure

```
float_tracker/
├── README.md                    # This file - project overview
├── INSTALL.md                   # Detailed installation guide
├── FloatTracker.xlsm           # Main Excel application
├── CreateFloatTracker.vbs      # Automated installer script
├── src/                        # VBA source code
│   ├── modules/                # VBA modules
│   │   ├── FloatTracker_MainModule.bas      # Core system logic
│   │   ├── FloatTracker_PINModule.bas       # PIN authentication
│   │   ├── FloatTracker_LoggingModule.bas   # Audit logging
│   │   ├── FloatTracker_AdminModule.bas     # Admin functions
│   │   └── FloatTracker_WorksheetModule.bas # Sheet management
│   ├── classes/                # VBA class modules
│   │   └── ThisWorkbook.cls    # Workbook event handlers
│   └── forms/                  # User interface forms
│       ├── FloatForm.frm       # Main float entry form
│       └── FloatForm.frx       # Form binary data
└── docs/                       # Documentation
    ├── user-guide.md           # End-user instructions
    └── admin-guide.md          # Administrator manual
```

## 🛠️ Installation

### Automated Installation (Recommended)
```bash
# 1. Download all files to a folder
# 2. Right-click CreateFloatTracker.vbs → Open with Windows Script Host
# 3. Wait for completion message
# 4. Open FloatTracker.xlsm and enable macros
```

### Manual Installation
See [INSTALL.md](INSTALL.md) for detailed step-by-step instructions.

## 📖 Documentation

- **[Installation Guide](INSTALL.md)** - Complete setup instructions
- **[User Guide](docs/user-guide.md)** - How to use the float system
- **[Admin Guide](docs/admin-guide.md)** - Administrative functions

## 🔧 System Requirements

### Required Software
- Microsoft Excel 365 (with VBA support)
- Windows 10/11 or compatible OS
- OneDrive account (recommended for auto-save)

### Required Permissions
- Ability to enable macros in Excel
- Permission to save .xlsm files
- Administrative access for user management

## 🔐 Security Features

### Data Protection
- **Encrypted PINs**: All PINs stored using secure hashing
- **Hidden Worksheets**: Sensitive data sheets are hidden from users
- **Password Protection**: Admin functions require authentication
- **Audit Logging**: Complete trail of all system actions

### Default Credentials (Change Immediately!)
- **Sample User PIN**: 1234
- **Admin Password**: FloatAdmin2024

## 🚦 Getting Started

### For End Users
1. Get your 4-digit PIN from your administrator
2. Click the "FLOAT" button on the main sheet
3. Enter your PIN and select your destination unit
4. System automatically updates the queue

### For Administrators
1. Click "Admin Panel" button
2. Enter admin password: `FloatAdmin2024`
3. Use menu options to manage users and system
4. See [Admin Guide](docs/admin-guide.md) for detailed instructions

## 📊 How It Works

### Queue Management
1. **Fair Rotation**: Staff are ordered in a queue
2. **Next Up Indicator**: Orange highlighting shows who's next
3. **Automatic Reordering**: After floating, you move to the bottom
4. **Hold System**: Staff can be temporarily removed (vacation, etc.)

### Float Process
1. Staff member clicks "FLOAT" button
2. Enters their unique 4-digit PIN
3. Selects destination unit from dropdown
4. System validates and processes the float
5. Queue automatically updates and saves

## 🔍 Troubleshooting

### Common Issues
- **Macros Disabled**: Enable macros in Excel Trust Center
- **FLOAT Button Not Working**: Check macro assignment in VBA
- **PIN Validation Fails**: Use Admin Panel to reset PIN
- **Colors Don't Update**: Run UpdateQueueColors macro manually

### Support
- Check documentation in `/docs/` folder
- Use emergency unlock: `Alt + F11` → Immediate Window → `ThisWorkbook.EmergencyUnlock`
- Contact your IT support team

## 📝 License

This project is provided as-is for internal organizational use. Modify and distribute according to your organization's policies.

## 🤝 Contributing

To modify the system:
1. Edit VBA source files in `/src/` folder
2. Import changes into Excel VBA Editor
3. Test thoroughly before deployment
4. Update documentation as needed

---

**⚠️ Important**: Always backup your data before making changes and test in a non-production environment first.
