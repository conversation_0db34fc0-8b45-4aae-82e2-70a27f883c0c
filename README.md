# Float Tracker System - Multi-Platform

A staff floating management system that ensures fair rotation through a secure, queue-based approach with complete audit trails. Available for both Microsoft 365 and Google Workspace environments.

## 🚀 Choose Your Platform

### 📊 Microsoft 365 (Excel + VBA)
**Best for:** Organizations using Microsoft 365 with Excel desktop access

**Features:**
- ✅ Full VBA automation
- ✅ Advanced PIN security system
- ✅ Comprehensive admin panel
- ✅ Real-time queue management
- ✅ Complete audit logging
- ✅ Auto-save functionality

**Requirements:** Excel desktop application (not Excel Web)

📁 **[Go to Microsoft 365 Version →](microsoft-365/)**

---

### 🌐 Google Sheets (Apps Script)
**Best for:** Organizations using Google Workspace or needing web-based access

**Features:**
- ✅ Web-based - works anywhere
- ✅ Apps Script automation
- ✅ PIN authentication system
- ✅ Queue management
- ✅ Audit logging
- ✅ Real-time collaboration
- ✅ Mobile-friendly interface

**Requirements:** Google account with Google Sheets access

📁 **[Go to Google Sheets Version →](google-sheets/)**

---

## 🎯 Quick Comparison

| Feature | Microsoft 365 | Google Sheets |
|---------|---------------|---------------|
| **Platform** | Excel Desktop | Web Browser |
| **Automation** | VBA | Apps Script |
| **Security** | Advanced | Good |
| **Mobile Access** | Limited | Excellent |
| **Offline Access** | Yes | Limited |
| **Setup Complexity** | Medium | Easy |
| **Multi-user** | Good | Excellent |
| **Cost** | M365 License | Free/G-Workspace |

## 🔧 Installation

### For Microsoft 365:
```bash
cd microsoft-365/
# Follow README.md in that folder
```

### For Google Sheets:
```bash
cd google-sheets/
# Follow README.md in that folder
```

## 📖 Documentation

Each platform has its own complete documentation:

- **Microsoft 365**: Full VBA-based system with desktop Excel
- **Google Sheets**: Web-based system with Apps Script automation

## 🤝 Contributing

Both versions are maintained in parallel. When adding features:
1. Implement in the appropriate platform folder
2. Update documentation for that platform
3. Consider if the feature should be ported to the other platform

## 📝 License

This project is provided as-is for internal organizational use. Modify and distribute according to your organization's policies.

---

**Choose the platform that best fits your organization's needs and technical environment!**
