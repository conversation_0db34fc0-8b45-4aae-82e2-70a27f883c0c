Attribute VB_Name = "MainModule"
' ===================================================================
' FLOAT TRACKER SYSTEM - MAIN MODULE
' Microsoft Excel 365 Float Management System
' ===================================================================

Option Explicit

' Global variables
Public Const ADMIN_PASSWORD As String = "FloatAdmin2024"
Public Const USERDATA_PASSWORD As String = "UserData2024"
Public Const STRUCTURE_PASSWORD As String = "Structure2024"

' ===================================================================
' MAIN FLOAT BUTTON HANDLER
' ===================================================================
Sub FloatButton_Click()
    On Error GoTo ErrorHandler
    
    ' Show the float form
    FloatForm.Show
    
    ' Process if valid submission
    If FloatForm.ValidSubmission Then
        Call ProcessFloat(FloatForm.StaffName, FloatForm.SelectedUnit, False)
    End If
    
    Exit Sub
    
ErrorHandler:
    MsgBox "Error processing float: " & Err.Description, vbCritical
End Sub

' ===================================================================
' CORE FLOAT PROCESSING
' ===================================================================
Sub ProcessFloat(staffName As String, selectedUnit As String, isAdminOverride As Boolean)
    On Error GoTo ErrorHandler
    
    Dim nextInQueue As String
    Dim currentDate As Date
    Dim currentPosition As Integer
    
    currentDate = Date
    nextInQueue = GetNextActiveStaff()
    currentPosition = GetQueuePosition(staffName)
    
    ' Check if someone else is floating when they weren't next
    If staffName <> nextInQueue And Not isAdminOverride Then
        ' Log that the next person was missed
        Call LogFloatAction(nextInQueue, "", GetQueuePosition(nextInQueue), "FLOAT_MISSED", "System", "Skipped when " & staffName & " floated")
        
        ' Set next person to red color
        Call SetStaffColor(nextInQueue, RGB(255, 200, 200)) ' Light red
    End If
    
    ' Process the actual float
    Call ReorderQueue(staffName)
    Call UpdateFloatInfo(staffName, selectedUnit)
    
    ' Log the float action
    If isAdminOverride Then
        Call LogFloatAction(staffName, selectedUnit, currentPosition, "ADMIN_OVERRIDE", GetCurrentAdmin(), "Admin override float")
    Else
        Call LogFloatAction(staffName, selectedUnit, currentPosition, "FLOAT", "", "")
    End If
    
    ' Check and correct missed floats for today
    Call CheckAndCorrectMissedFloats(currentDate)
    
    ' Update all colors
    Call UpdateQueueColors()

    ' Auto-save after critical float operation
    Call SafeSave("Float: " & staffName & " to " & selectedUnit)

    ' Show confirmation
    MsgBox "Float processed successfully for " & staffName & " to " & selectedUnit, vbInformation
    
    Exit Sub
    
ErrorHandler:
    MsgBox "Error in ProcessFloat: " & Err.Description, vbCritical
End Sub

' ===================================================================
' QUEUE MANAGEMENT FUNCTIONS
' ===================================================================
Function GetNextActiveStaff() As String
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim i As Long
    
    Set ws = ThisWorkbook.Sheets("Float Queue")
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
    
    ' Find first active staff member
    For i = 2 To lastRow
        If ws.Cells(i, 1).Value <> "" And ws.Cells(i, 4).Value = "Active" Then
            GetNextActiveStaff = ws.Cells(i, 1).Value
            Exit Function
        End If
    Next i
    
    GetNextActiveStaff = ""
End Function

Function GetQueuePosition(staffName As String) As Integer
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim i As Long
    Dim position As Integer
    
    Set ws = ThisWorkbook.Sheets("Float Queue")
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
    position = 1
    
    For i = 2 To lastRow
        If ws.Cells(i, 1).Value <> "" And ws.Cells(i, 4).Value = "Active" Then
            If ws.Cells(i, 1).Value = staffName Then
                GetQueuePosition = position
                Exit Function
            End If
            position = position + 1
        End If
    Next i
    
    GetQueuePosition = 0
End Function

Sub ReorderQueue(staffName As String)
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim i As Long
    Dim staffRow As Long
    Dim tempValues As Variant
    
    Set ws = ThisWorkbook.Sheets("Float Queue")
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
    
    ' Find the staff member's row
    For i = 2 To lastRow
        If ws.Cells(i, 1).Value = staffName Then
            staffRow = i
            Exit For
        End If
    Next i
    
    If staffRow = 0 Then Exit Sub
    
    ' Store the row data
    tempValues = ws.Range(ws.Cells(staffRow, 1), ws.Cells(staffRow, 5)).Value
    
    ' Delete the row
    ws.Rows(staffRow).Delete
    
    ' Add at the bottom
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row + 1
    ws.Range(ws.Cells(lastRow, 1), ws.Cells(lastRow, 5)).Value = tempValues
End Sub

Sub UpdateFloatInfo(staffName As String, selectedUnit As String)
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim i As Long
    
    Set ws = ThisWorkbook.Sheets("Float Queue")
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
    
    For i = 2 To lastRow
        If ws.Cells(i, 1).Value = staffName Then
            ws.Cells(i, 2).Value = Now() ' Last Floated
            ws.Cells(i, 3).Value = selectedUnit ' Float Unit
            Exit For
        End If
    Next i
End Sub

' ===================================================================
' COLOR MANAGEMENT
' ===================================================================
Sub UpdateQueueColors()
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim i As Long
    Dim position As Integer
    
    Set ws = ThisWorkbook.Sheets("Float Queue")
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
    position = 1
    
    For i = 2 To lastRow
        If ws.Cells(i, 1).Value <> "" Then
            Select Case ws.Cells(i, 4).Value ' Status column
                Case "Active"
                    If position = 1 Then
                        ' Orange - Next to float
                        ws.Range(ws.Cells(i, 1), ws.Cells(i, 5)).Interior.Color = RGB(255, 200, 100)
                    Else
                        ' Green - Normal queue
                        ws.Range(ws.Cells(i, 1), ws.Cells(i, 5)).Interior.Color = RGB(200, 255, 200)
                    End If
                    position = position + 1
                    
                Case "On Hold"
                    ' Gray - On hold
                    ws.Range(ws.Cells(i, 1), ws.Cells(i, 5)).Interior.Color = RGB(200, 200, 200)
                    
                Case Else
                    ' Default white
                    ws.Range(ws.Cells(i, 1), ws.Cells(i, 5)).Interior.Color = RGB(255, 255, 255)
            End Select
        End If
    Next i
End Sub

Sub SetStaffColor(staffName As String, colorRGB As Long)
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim i As Long
    
    Set ws = ThisWorkbook.Sheets("Float Queue")
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
    
    For i = 2 To lastRow
        If ws.Cells(i, 1).Value = staffName Then
            ws.Range(ws.Cells(i, 1), ws.Cells(i, 5)).Interior.Color = colorRGB
            Exit For
        End If
    Next i
End Sub

' ===================================================================
' UTILITY FUNCTIONS
' ===================================================================
Function GetCurrentAdmin() As String
    ' This would be set when admin logs in
    ' For now, return a default value
    GetCurrentAdmin = "Admin"
End Function

Function UserExists(staffName As String) As Boolean
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim i As Long

    Set ws = ThisWorkbook.Sheets("Float Queue")
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row

    For i = 2 To lastRow
        If ws.Cells(i, 1).Value = staffName Then
            UserExists = True
            Exit Function
        End If
    Next i

    UserExists = False
End Function

' ===================================================================
' MISSED FLOAT CORRECTION LOGIC
' ===================================================================
Sub CheckAndCorrectMissedFloats(checkDate As Date)
    On Error GoTo ErrorHandler

    Dim dailyFloatCount As Integer
    Dim logSheet As Worksheet
    Dim lastRow As Long, i As Long

    Set logSheet = ThisWorkbook.Sheets("Float Log")
    lastRow = logSheet.Cells(logSheet.Rows.Count, 1).End(xlUp).Row

    ' Count total floats (regular + admin) for the day
    dailyFloatCount = 0
    For i = 2 To lastRow
        If DateValue(logSheet.Cells(i, 2).Value) = checkDate Then
            If logSheet.Cells(i, 6).Value = "FLOAT" Or logSheet.Cells(i, 6).Value = "ADMIN_OVERRIDE" Then
                dailyFloatCount = dailyFloatCount + 1
            End If
        End If
    Next i

    ' If multiple floats occurred today, correct any missed floats
    If dailyFloatCount > 1 Then
        For i = 2 To lastRow
            If DateValue(logSheet.Cells(i, 2).Value) = checkDate And logSheet.Cells(i, 6).Value = "FLOAT_MISSED" Then
                ' Log correction
                Call LogFloatAction(logSheet.Cells(i, 3).Value, "", 0, "FLOAT_MISSED_CORRECTED", "System", "Multiple floats same day - missed float corrected")

                ' Remove red color from staff (back to normal queue color)
                Call UpdateQueueColors()
            End If
        Next i
    End If

    Exit Sub

ErrorHandler:
    MsgBox "Error in CheckAndCorrectMissedFloats: " & Err.Description, vbCritical
End Sub

' ===================================================================
' AUTO-SAVE SAFETY FUNCTIONS
' ===================================================================
Sub SafeSave(Optional operation As String = "")
    On Error GoTo ErrorHandler

    ' Only save if there are unsaved changes
    If Not ThisWorkbook.Saved Then
        ThisWorkbook.Save

        ' Optional: Show brief status message for critical operations
        If operation <> "" Then
            Application.StatusBar = "Saved: " & operation & " - " & Format(Now(), "hh:mm:ss")
            ' Clear status bar after 3 seconds
            Application.OnTime Now + TimeValue("00:00:03"), "ClearStatusBar"
        End If
    End If

    Exit Sub

ErrorHandler:
    ' Handle save errors gracefully - don't interrupt user workflow
    If operation <> "" Then
        Application.StatusBar = "Save failed: " & operation & " - Please save manually"
        Application.OnTime Now + TimeValue("00:00:05"), "ClearStatusBar"
    End If
End Sub

Sub ClearStatusBar()
    On Error Resume Next
    Application.StatusBar = False
End Sub

Sub ForceBackupSave()
    On Error GoTo ErrorHandler

    ' Force save regardless of saved status (for admin operations)
    ThisWorkbook.Save
    Application.StatusBar = "Backup save completed - " & Format(Now(), "hh:mm:ss")
    Application.OnTime Now + TimeValue("00:00:03"), "ClearStatusBar"

    Exit Sub

ErrorHandler:
    Application.StatusBar = "Backup save failed - Please save manually"
    Application.OnTime Now + TimeValue("00:00:05"), "ClearStatusBar"
End Sub
