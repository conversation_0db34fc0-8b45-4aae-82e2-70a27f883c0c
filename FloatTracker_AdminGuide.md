# Float Tracker System - Administrator Guide

## Overview
This guide covers administrative functions for the Float Tracker System, including user management, system overrides, and maintenance procedures.

## Admin Access

### Logging In
1. Click the "Admin Panel" button on the Float Queue sheet
2. Enter the admin password: `FloatAdmin2024`
3. Enter your admin name for logging purposes
4. Access granted - admin menu will appear

### Admin Menu Options
```
1 - Override Float (Float someone without PIN)
2 - Add New User
3 - Remove User  
4 - Reset User PIN
5 - Hold Staff (Vacation/Unavailable)
6 - Release Staff from Hold
7 - Reset Queue Order
8 - View Float Summary
9 - View Missed Floats
0 - Exit Admin Panel
```

## User Management

### Adding New Users
1. Select option "2 - Add New User"
2. Enter the staff member's full name
3. System generates a random 4-digit PIN
4. **CRITICAL**: Write down the PIN immediately - it will only be shown once
5. Give the PIN to the staff member privately
6. User is added to the bottom of the queue

**Security Notes:**
- PINs are never stored in plain text
- PINs are never shown again after creation
- Each PIN is unique across the system

### Removing Users
1. Select option "3 - Remove User"
2. Enter the staff member's name
3. Confirm the removal
4. User is removed from queue and PIN is deactivated
5. Float history is preserved for audit purposes

### Resetting PINs
1. Select option "4 - Reset User PIN"
2. Enter the staff member's name
3. System generates a new random PIN
4. **CRITICAL**: Write down the new PIN immediately
5. Give the new PIN to the staff member privately

## Staff Hold Management

### Putting Staff on Hold
Use this for vacation, sick leave, or temporary unavailability:

1. Select option "5 - Hold Staff"
2. Enter the staff member's name
3. Enter reason (e.g., "Vacation 8/1-8/15")
4. Staff member is marked "On Hold" and grayed out
5. They are skipped in queue rotation until released

### Releasing Staff from Hold
1. Select option "6 - Release Staff from Hold"
2. View current holds list
3. Enter staff member's name to release
4. They return to active status in queue

## Administrative Overrides

### Override Float
Use for emergency situations or corrections:

1. Select option "1 - Override Float"
2. View available active staff list
3. Enter staff member's name
4. Select unit from available units
5. Confirm the override
6. Action is logged as "ADMIN_OVERRIDE"

**When to Use:**
- Emergency coverage needed
- Correcting mistakes
- Special assignments
- Coverage gaps

### Reset Queue Order
Use sparingly - completely randomizes the queue:

1. Select option "7 - Reset Queue Order"
2. Confirm the action (cannot be undone)
3. All active staff are randomly reordered
4. Staff on hold remain in current positions
5. Action is logged for audit

## Reporting and Analysis

### Float Summary Report
- Shows total floats by staff member
- Identifies usage patterns
- Helps ensure fair distribution

### Missed Floats Report
- Shows uncorrected missed floats
- Staff who were skipped in rotation
- Helps identify fairness issues

## System Maintenance

### Password Management
Current system passwords:
- **Admin Password**: `FloatAdmin2024`
- **UserData Protection**: `UserData2024`
- **Structure Protection**: `Structure2024`

### Emergency Procedures

#### Emergency Unlock
If system becomes corrupted or locked:
1. Press Alt+F11 to open VBA editor
2. In Immediate window, type: `ThisWorkbook.EmergencyUnlock`
3. Enter emergency password: `EMERGENCYFloatAdmin2024`
4. All protections will be removed

#### Restore Protection
After emergency unlock:
1. In VBA Immediate window, type: `ThisWorkbook.RestoreProtection`
2. All protections will be restored

### Backup Procedures
1. Save regular backups of the .xlsm file
2. Export user data periodically
3. Keep copies of float logs
4. Document any system changes

## Troubleshooting

### Common Issues

#### "User not found" errors
- Check spelling of staff name
- Verify user exists in system
- Check if user was previously removed

#### PIN validation failures
- Verify user is "Active" status
- Check if PIN was recently reset
- Confirm user is entering correct PIN

#### Button/macro errors
- Ensure macros are enabled
- Check for Excel security settings
- Verify file hasn't been corrupted

### System Recovery
If data becomes corrupted:
1. Restore from most recent backup
2. Use emergency unlock if needed
3. Verify all user PINs still work
4. Check log integrity

## Security Best Practices

### PIN Security
- Never write PINs in emails or documents
- Give PINs to users privately
- Reset PINs if compromised
- Monitor for unusual activity

### Access Control
- Limit admin access to authorized personnel
- Change admin password periodically
- Log all administrative actions
- Review logs regularly

### Data Protection
- Keep backups secure
- Limit file sharing
- Protect against unauthorized access
- Monitor system usage

## Advanced Features

### Missed Float Logic
The system automatically:
- Detects when someone is skipped in rotation
- Marks them with red highlighting
- Corrects the missed float if multiple people float same day
- Logs all missed float events and corrections

### Audit Trail
Every action is logged with:
- Timestamp
- Staff member involved
- Action type
- Admin name (for admin actions)
- Detailed notes

## Contact and Support

### For System Issues
- Check this guide first
- Try emergency procedures if needed
- Contact IT support for technical problems
- Document any recurring issues

### For Policy Questions
- Review organizational float policies
- Consult with nursing leadership
- Update system configuration as needed

---

*Float Tracker System v1.0 - Administrator Guide*
*Keep this document secure and confidential*
